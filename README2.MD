# 📝 Note Taking App

A comprehensive note-taking application built with **Vue.js 3** and **Node.js**, supporting multiple note formats (**Rich Text, Markdown, Kanban**), **real-time collaboration in groups**, and **secure sharing**.

Perfect for individuals and teams who want flexibility, performance, and privacy in their digital workspace.

![App Screenshot (example)](./screenshots/editor.png)  
*Example: Multi-format editor with live preview*

🔗 [Live Demo](https://demo.notetaker.app) | 📚 [Documentation](./docs/README.md)



## 🗂️ Project Structure

├── frontend/ # Vue.js 3 + TypeScript frontend
│ ├── src/
│ │ ├── assets/
│ │ │ └── styles/ # Tailwind + custom base/components/utilities
│ │ ├── components/ # Reusable UI components
│ │ ├── views/ # Page-level components
│ │ ├── stores/ # Pinia state management
│ │ └── main.ts
│ └── package.json
├── backend/ # Node.js + Express + TypeScript backend
│ ├── src/
│ │ ├── config/ # DB connection, environment setup
│ │ ├── routes/ # API endpoints
│ │ ├── models/ # Data models and queries
│ │ ├── services/ # Business logic
│ │ └── index.ts # Server entry point
│ └── package.json
├── docs/ # Project documentation
├── screenshots/ # Optional: UI previews
└── README.md


## ⚙️ Development Setup

### Prerequisites

- [Node.js](https://nodejs.org/) v22.11.0 or higher
- `npm` (comes with Node)
- MariaDB (running locally or via Docker)
- Recommended: VS Code with ESLint & Prettier extensions

### Backend Setup

```bash
cd backend
cp .env.example .env  # Edit credentials as needed
npm install
npm run dev
🌐 Backend runs on http://localhost:3000 
```

Frontend Setup
```bash
cd frontend
npm install
npm run dev
💻 Frontend available at http://localhost:5173 
```

🔐 Environment Configuration
Create .env from the example:

# backend/.env.example
PORT=3000
DB_HOST=localhost
DB_USER=root
DB_PASS=password
DB_NAME=notetaker
JWT_SECRET=your_strong_jwt_secret_here
NODE_ENV=development
✅ Copy .env.example → .env and update values for your local database. 

✨ Features
Multi-format Notes
Rich Text (WYSIWYG editor)
Markdown with live preview
Kanban boards for task organization
Real-time Collaboration
Live co-editing using Socket.IO
Cursor presence indicators
Conflict-aware updates (planned: OT/CRDT)
Secure Sharing
Share notes by user or public link
Permission levels: View, Comment, Edit
Expiry and password protection (future)
Responsive Design
Fully responsive layout
Works seamlessly on desktop, tablet, and mobile
Full-text Search
Fast search across titles, content, and tags
Indexed via SQL full-text capabilities
User Management
Email/password authentication
Role-based access control (Admin, Member, Guest)
Group workspaces support
Export Options
Export to PDF, Markdown (.md), or HTML
One-click download
💻 Technology Stack
Frontend
Vue.js 3
Reactive UI framework
Composition API
Logical code organization
TypeScript
Type safety
Pinia
State management
Vue Router
Client-side routing
Tailwind CSS
Utility-first styling
Vite
Blazing-fast dev server & build tool
Vitest
Unit testing
🎨 Styling Architecture:
Custom classes extracted into: 

base.css: Resets and globals
components.css: Component-specific styles via @apply
utilities.css: Project-wide helpers
Backend
Node.js + Express
REST API server
TypeScript
Type-safe backend
MariaDB
Primary relational database
JWT
Secure authentication
Socket.IO
Real-time communication
ESLint/Prettier
Code quality & formatting
Jest
Unit and integration tests
🛠️ ORM Layer: Raw SQL with helper query builders (future: consider TypeORM) 

🛠️ Development Commands
Frontend
npm run dev
Start development server
npm run build
Build for production
npm run preview
Preview production build locally
npm run lint
Run ESLint
npm run format
Format code with Prettier
npm run test
Run unit tests (Vitest)
Backend
npm run dev
Start dev server with hot reload (nodemon)
npm run build
Compile TypeScript to JavaScript
npm start
Start production server
npm run lint
Lint codebase
npm run format
Format code with Prettier
npm run test
Run unit/integration tests (Jest)
📁 Additional Resources
📚 Documentation
Check /docs/ for:

API Specification
Database Schema
Contribution Guidelines
Architecture Decisions (ADR)
🤝 Contributing
We welcome contributions! Please read our Contribution Guide before submitting pull requests.

🛑 Security
To report a security vulnerability, please email: <EMAIL> (or use private channels).

🎯 Roadmap
Planned features:

Offline sync using IndexedDB
Version history & undo timeline
Dark mode toggle
Mobile app via Capacitor
Calendar view for Kanban deadlines
OAuth (Google, GitHub)