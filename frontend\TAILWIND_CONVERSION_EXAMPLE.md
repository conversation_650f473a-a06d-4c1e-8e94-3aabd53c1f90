# Tailwind CSS Conversion Example

This document shows how to convert components from the current Bulma-based system to Tailwind CSS.

## Before (Bulma Classes)

```vue
<template>
  <div class="auth-form">
    <div class="form-content">
      <form @submit.prevent="handleSubmit">
        <!-- Email Field -->
        <div class="field">
          <label class="label">Email</label>
          <div class="control has-icons-left">
            <input 
              v-model="form.email" 
              type="email" 
              class="input"
              :class="{ 'is-danger': emailValidation.errors.length > 0 && emailTouched }" 
              placeholder="Enter your email"
              @blur="emailTouched = true" 
              @input="validateEmail" 
              :disabled="authStore.isLoading" 
            />
            <span class="icon is-small is-left email-icon">
              <i class="fas fa-envelope"></i>
            </span>
          </div>
          <div v-if="emailValidation.errors.length > 0 && emailTouched" class="help is-danger">
            <div v-for="error in emailValidation.errors" :key="error">
              {{ error }}
            </div>
          </div>
        </div>

        <!-- Submit Button -->
        <div class="field">
          <div class="control">
            <button 
              type="submit" 
              class="button is-primary is-fullwidth"
              :class="{ 'is-loading': authStore.isLoading }"
              :disabled="!isFormValid || authStore.isLoading"
            >
              Sign In
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>
```

## After (Tailwind CSS Classes)

```vue
<template>
  <div class="max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
    <div class="space-y-6">
      <form @submit.prevent="handleSubmit" class="space-y-4">
        <!-- Email Field -->
        <div class="form-field">
          <label class="form-label">Email</label>
          <div class="form-control has-icon-left">
            <input 
              v-model="form.email" 
              type="email" 
              class="form-input"
              :class="{ 'is-danger': emailValidation.errors.length > 0 && emailTouched }" 
              placeholder="Enter your email"
              @blur="emailTouched = true" 
              @input="validateEmail" 
              :disabled="authStore.isLoading" 
            />
            <span class="form-icon">
              <i class="fas fa-envelope"></i>
            </span>
          </div>
          <div v-if="emailValidation.errors.length > 0 && emailTouched" class="form-help is-danger">
            <div v-for="error in emailValidation.errors" :key="error">
              {{ error }}
            </div>
          </div>
        </div>

        <!-- Submit Button -->
        <div class="form-field">
          <button 
            type="submit" 
            class="btn btn-primary w-full"
            :class="{ 'is-loading': authStore.isLoading }"
            :disabled="!isFormValid || authStore.isLoading"
          >
            Sign In
          </button>
        </div>
      </form>
    </div>
  </div>
</template>
```

## Class Mapping Reference

### Layout Classes
| Bulma | Tailwind Equivalent |
|-------|-------------------|
| `.container` | `.container-lg` (custom) or `.max-w-screen-lg mx-auto px-4` |
| `.section` | `.py-12` or `.py-16` |
| `.hero` | `.bg-gradient-to-r from-primary-500 to-secondary-500 py-20` |
| `.columns` | `.grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6` |
| `.column` | `.col-span-1` |

### Form Classes
| Bulma | Tailwind Equivalent |
|-------|-------------------|
| `.field` | `.form-field` (custom component class) |
| `.label` | `.form-label` (custom component class) |
| `.control` | `.form-control` (custom component class) |
| `.input` | `.form-input` (custom component class) |
| `.button` | `.btn` (custom component class) |
| `.is-primary` | `.btn-primary` (custom component class) |
| `.is-fullwidth` | `.w-full` |
| `.is-loading` | `.is-loading` (custom utility class) |

### State Classes
| Bulma | Tailwind Equivalent |
|-------|-------------------|
| `.is-danger` | `.is-danger` (custom component class) |
| `.is-success` | `.is-success` (custom component class) |
| `.is-warning` | `.is-warning` (custom component class) |
| `.is-info` | `.is-info` (custom component class) |
| `.help` | `.form-help` (custom component class) |

### Utility Classes
| Bulma | Tailwind Equivalent |
|-------|-------------------|
| `.has-text-centered` | `.text-center` |
| `.has-text-left` | `.text-left` |
| `.has-text-right` | `.text-right` |
| `.is-hidden` | `.hidden` |
| `.is-flex` | `.flex` |
| `.is-justify-content-center` | `.justify-center` |
| `.is-align-items-center` | `.items-center` |

## Benefits of the Conversion

1. **Smaller Bundle Size**: Tailwind's purging removes unused CSS
2. **Better Performance**: Optimized CSS delivery
3. **Consistent Design System**: Unified spacing, colors, and typography
4. **Dark Mode Support**: Built-in dark mode with class-based switching
5. **Better Developer Experience**: IntelliSense support and utility-first approach
6. **Maintainability**: Component classes with @apply for reusability

## Migration Strategy

1. **Phase 1**: Set up Tailwind CSS infrastructure (✅ Complete)
2. **Phase 2**: Create component classes using @apply (✅ Complete)
3. **Phase 3**: Convert core components (auth, layout, navigation)
4. **Phase 4**: Convert feature components (notes, kanban, admin)
5. **Phase 5**: Remove old CSS files and optimize bundle

## Custom Component Classes

The conversion uses custom component classes defined in `components.css` that use Tailwind's `@apply` directive:

```css
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md bg-white text-gray-700 font-medium text-sm leading-none transition-all duration-200 cursor-pointer select-none whitespace-nowrap;
}

.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-700 placeholder-gray-400 transition-colors duration-200;
}
```

This approach provides:
- **Consistency**: All buttons and inputs look the same
- **Maintainability**: Easy to update styles in one place
- **Flexibility**: Can still use Tailwind utilities for customization
- **Migration**: Easier transition from Bulma class names
