{"name": "frontend", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --project tsconfig.app.json --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "format": "prettier --write src/", "analyze": "npm run build && npx serve dist", "bundle-analyze": "npm run build && open dist/stats.html", "perf-budget": "npm run build && node scripts/check-bundle-size.js", "build:analyze": "node scripts/build-with-analysis.js", "build:production_analyze": "NODE_ENV=production npm run build:analyze", "perf-check": "node scripts/performance-budget-check.js", "perf-regression": "node scripts/performance-regression-check.js", "perf-baseline": "node scripts/performance-regression-check.js --update-baseline", "perf-full": "npm run build && node scripts/check-bundle-size.js && node scripts/performance-budget-check.js && node scripts/performance-regression-check.js", "perf-test": "node scripts/run-performance-tests.js", "perf-test-full": "node scripts/run-performance-tests.js --run-tests", "perf-regression-test": "node scripts/run-performance-regression-tests.js", "perf-regression-test-update": "node scripts/run-performance-regression-tests.js --update-baseline", "build:optimized": "node scripts/optimize-build.js", "build:production": "NODE_ENV=production npm run build:optimized"}, "dependencies": {"@fortawesome/fontawesome-free": "^7.0.0", "@tiptap/extension-bullet-list": "^3.2.0", "@tiptap/extension-color": "^3.2.0", "@tiptap/extension-highlight": "^3.2.0", "@tiptap/extension-image": "^3.2.0", "@tiptap/extension-link": "^3.2.0", "@tiptap/extension-list-item": "^3.2.0", "@tiptap/extension-ordered-list": "^3.2.0", "@tiptap/extension-table": "^3.2.0", "@tiptap/extension-table-cell": "^3.2.0", "@tiptap/extension-table-header": "^3.2.0", "@tiptap/extension-table-row": "^3.2.0", "@tiptap/extension-text-align": "^3.2.0", "@tiptap/extension-text-style": "^3.2.0", "@tiptap/starter-kit": "^3.2.0", "@tiptap/vue-3": "^3.2.0", "@types/marked": "^5.0.2", "chart.js": "^4.5.0", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^4.1.0", "google-one-tap": "^1.0.6", "highlight.js": "^11.11.1", "lodash-es": "^4.17.21", "marked": "^16.2.0", "pinia": "^3.0.3", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@fullhuman/postcss-purgecss": "^7.0.2", "@pinia/testing": "^1.0.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.13", "@tailwindcss/typography": "^0.5.16", "@tsconfig/node22": "^22.0.2", "@types/jsdom": "^21.1.7", "@types/lodash-es": "^4.17.12", "@types/node": "^22.16.5", "@typescript-eslint/eslint-plugin": "^8.40.0", "@typescript-eslint/parser": "^8.40.0", "@vitejs/plugin-vue": "^6.0.1", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-vue": "^10.4.0", "jsdom": "^26.1.0", "npm-run-all2": "^8.0.4", "postcss": "^8.5.6", "prettier": "^3.6.2", "rollup-plugin-visualizer": "^6.0.3", "sass-embedded": "^1.90.0", "tailwindcss": "^4.1.13", "terser": "^5.43.1", "typescript": "~5.8.0", "vite": "^7.1.3", "vite-plugin-vue-devtools": "^8.0.0", "vitest": "^3.2.4", "vue-tsc": "^3.0.6"}}