/* Dropdown Components - Theme Integration */

/* Base dropdown styling */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown.is-active .dropdown-menu {
  display: block;
}

.dropdown-trigger {
  cursor: pointer;
}

/* Dropdown menu container */
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000 !important;
  display: none;
  min-width: 12rem;
  padding-top: 0;
}

/* Right-aligned dropdowns */
.dropdown.is-right .dropdown-menu {
  left: auto;
  right: 0;
}

/* Dropdown content box */
.dropdown-content {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  box-shadow: var(--shadow-lg);
  padding: 0.5rem 0;
  max-height: 20rem;
  overflow-y: auto;
  margin-top: 0;
  z-index: 1001 !important;
  position: relative !important;
}

/* Dropdown items */
.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: var(--color-text);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  border: none;
  background: transparent;
  width: 100%;
  text-align: left;
  font-size: var(--font-size-sm);
  line-height: 1.2;
}

.dropdown-item:hover,
.dropdown-item:focus {
  background: var(--color-surface-hover);
  color: var(--color-primary);
  outline: none;
}

.dropdown-item:active {
  background: var(--color-primary-alpha);
}

/* Dropdown item icons */
.dropdown-item .icon {
  flex-shrink: 0;
  width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.8;
  transition: opacity var(--transition-fast);
  font-size: 0.875rem;
}

/* Ensure icons and text are properly aligned */
.dropdown-item > .icon {
  margin-right: 0;
}

.dropdown-item > span:not(.icon) {
  flex: 1;
  display: inline-block;
}

/* Force horizontal layout for dropdown items */
.dropdown-content .dropdown-item {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  gap: 0.75rem !important;
}

/* Specific styling for user dropdown items */
.user-actions .dropdown-item {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  gap: 0.75rem !important;
  padding: 0.75rem 1rem !important;
}

.user-actions .dropdown-item .icon {
  flex-shrink: 0 !important;
  width: 1.25rem !important;
  height: 1.25rem !important;
  margin-right: 0 !important;
}

.user-actions .dropdown-item span:not(.icon) {
  flex: 1 !important;
  white-space: nowrap !important;
}

.dropdown-item:hover .icon,
.dropdown-item:focus .icon {
  opacity: 1;
}

/* Dropdown divider */
.dropdown-divider {
  height: 1px;
  margin: 0.25rem 0;
  background: var(--color-border);
  border: none;
  opacity: 0.3;
}

/* User dropdown specific divider */
.user-actions .dropdown-divider {
  margin: 0.5rem 0;
  background: var(--color-border);
  opacity: 0.2;
}

/* Special dropdown item types */
.dropdown-item.is-danger {
  color: var(--color-danger);
}

.dropdown-item.is-danger:hover,
.dropdown-item.is-danger:focus {
  background: var(--color-danger-alpha);
  color: var(--color-danger);
}

.dropdown-item.is-warning {
  color: var(--color-warning);
}

.dropdown-item.is-warning:hover,
.dropdown-item.is-warning:focus {
  background: var(--color-warning-alpha);
  color: var(--color-warning);
}

/* User dropdown specific styling */
.user-actions .dropdown-menu {
  min-width: 10rem;
}

.user-actions .dropdown-item {
  font-size: 0.875rem;
}

/* Note actions dropdown styling */
.note-actions .dropdown-menu,
.note-item-actions .dropdown-menu {
  min-width: 8rem;
  z-index: 10000;
}

.note-actions .dropdown-content,
.note-item-actions .dropdown-content {
  padding: 0.25rem 0;
}

.note-actions .dropdown-item,
.note-item-actions .dropdown-item {
  padding: 0.375rem 0.75rem;
  font-size: 0.8125rem;
}

/* Dropdown animations */
.dropdown-menu {
  opacity: 0;
  transform: translateY(-0.5rem);
  transition: all var(--transition-fast);
  pointer-events: none;
}

.dropdown.is-active .dropdown-menu {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .dropdown-menu {
    min-width: 10rem;
    max-width: calc(100vw - 2rem);
  }

  .dropdown.is-right .dropdown-menu {
    left: auto;
    right: 0;
    transform-origin: top right;
  }

  .dropdown-item {
    padding: 0.75rem 1rem;
    font-size: 0.9375rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .dropdown-content {
    border-width: 2px;
  }

  .dropdown-item {
    border-bottom: 1px solid transparent;
  }

  .dropdown-item:hover,
  .dropdown-item:focus {
    border-bottom-color: var(--color-primary);
  }
}

/* Dark theme specific adjustments */
[data-theme*='dark'] .dropdown-content,
[data-theme*='cyborg'] .dropdown-content,
[data-theme*='slate'] .dropdown-content {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Light theme specific adjustments */
[data-theme*='light'] .dropdown-content,
[data-theme*='cerulean'] .dropdown-content,
[data-theme*='flatly'] .dropdown-content {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Focus trap for accessibility */
.dropdown.is-active .dropdown-content {
  outline: none;
}

.dropdown-item:focus {
  position: relative;
}

.dropdown-item:focus::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px solid var(--color-primary);
  border-radius: var(--radius-sm);
  pointer-events: none;
}

/* Scrollbar styling for long dropdown lists */
.dropdown-content::-webkit-scrollbar {
  width: 0.25rem;
}

.dropdown-content::-webkit-scrollbar-track {
  background: var(--color-surface);
}

.dropdown-content::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: var(--radius-sm);
}

.dropdown-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-muted);
}

/* Dropdown item layout consistency */
.dropdown-item,
.dropdown-content .dropdown-item,
.user-actions .dropdown-item,
a.dropdown-item,
router-link.dropdown-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 0.75rem;
  text-align: left;
  white-space: nowrap;
}

/* Icon and text alignment */
.dropdown-item .icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
  margin: 0;
}

.dropdown-item > span:not(.icon) {
  display: inline-block;
  flex: 1;
  line-height: 1.2;
}

/* Proper spacing */
.dropdown-content {
  min-width: 12rem;
  padding: 0.5rem 0;
}

.user-actions .dropdown-content {
  min-width: 10rem;
}

/* Clean dropdown positioning */
.dropdown-menu {
  margin-top: 0;
  padding-top: 0;
}

.dropdown-content {
  margin-top: 0;
  border: 1px solid var(--color-border);
}

/* Theme-specific adjustments */
[data-theme='solarized'] .dropdown-content {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Remove pseudo-elements */
.dropdown-menu::before,
.dropdown-content::before,
.dropdown-menu::after,
.dropdown-content::after {
  display: none;
}

/* Small action button consistency - moved to buttons.css */
/* Clean dropdown styling without aggressive overrides */
.dropdown-menu,
.dropdown-content {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  margin: 0;
  padding-top: 0;
  outline: none;
}

/* Ensure clean first child styling */
.dropdown-menu > *:first-child,
.dropdown-content > *:first-child {
  margin-top: 0;
  border-top: none;
}

/* Clean top edge for dropdown content */
.dropdown-content {
  padding-top: 0.5rem;
  border-top-left-radius: var(--radius);
  border-top-right-radius: var(--radius);
}
