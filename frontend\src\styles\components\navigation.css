/* Navigation Component Styles */

/* Navbar */
.navbar {
  display: flex;
  align-items: center;
  padding: var(--spacing-4);
  background: var(--navbar-background);
  border-bottom: 1px solid var(--color-border);
  min-height: 3.25rem;
  position: relative;
  z-index: var(--z-sticky);
}

.navbar.is-fixed-top {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-fixed);
}

.navbar.is-fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: var(--z-fixed);
  border-top: 1px solid var(--color-border);
  border-bottom: none;
}

/* Navbar Brand */
.navbar-brand {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  min-height: 3.25rem;
}

.navbar-brand .title {
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-xl);
  margin: 0;
  color: var(--navbar-text);
}

.navbar-brand .navbar-item {
  padding: 0;
}

/* Navbar Menu */
.navbar-menu {
  display: flex;
  margin-left: auto;
  align-items: center;
  flex-grow: 1;
  flex-shrink: 0;
}

.navbar-start {
  display: flex;
  align-items: center;
  margin-right: auto;
}

.navbar-end {
  display: flex;
  align-items: center;
  margin-left: auto;
}

/* Navbar Items */
.navbar-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-2) var(--spacing-3);
  text-decoration: none;
  color: var(--navbar-text);
  transition: var(--transition-fast);
  border-radius: var(--radius);
  white-space: nowrap;
  cursor: pointer;
}

.navbar-item:hover {
  background: var(--navbar-item-hover);
  color: var(--color-primary);
}

.navbar-item:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.navbar-item.is-active {
  background: var(--color-primary);
  color: white;
}

.navbar-item.has-dropdown {
  position: relative;
}

/* Navbar Links */
.navbar-link {
  display: flex;
  align-items: center;
  padding: var(--spacing-2) var(--spacing-3);
  color: var(--navbar-text);
  cursor: pointer;
  transition: var(--transition-fast);
}

.navbar-link:hover {
  background: var(--navbar-item-hover);
  color: var(--color-primary);
}

.navbar-link::after {
  content: '';
  margin-left: var(--spacing-2);
  border: 0.35em solid transparent;
  border-top-color: currentColor;
  transform: rotate(0deg);
  transition: var(--transition-fast);
}

.navbar-link.is-active::after {
  transform: rotate(180deg);
}

/* Navbar Dropdown */
.navbar-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  background: var(--dropdown-background);
  border: 1px solid var(--dropdown-border);
  border-radius: var(--radius);
  box-shadow: var(--shadow-lg);
  min-width: 200px;
  z-index: var(--z-dropdown);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: var(--transition-fast);
}

.navbar-item.has-dropdown:hover .navbar-dropdown,
.navbar-item.has-dropdown.is-active .navbar-dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.navbar-dropdown .navbar-item {
  display: block;
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: 0;
}

.navbar-dropdown .navbar-item:first-child {
  border-top-left-radius: var(--radius);
  border-top-right-radius: var(--radius);
}

.navbar-dropdown .navbar-item:last-child {
  border-bottom-left-radius: var(--radius);
  border-bottom-right-radius: var(--radius);
}

.navbar-dropdown .navbar-divider {
  height: 1px;
  background: var(--color-border);
  margin: var(--spacing-1) 0;
}

/* Navbar Burger (Mobile Menu Toggle) */
.navbar-burger {
  display: none;
  cursor: pointer;
  width: 3.25rem;
  height: 3.25rem;
  position: relative;
  margin-left: auto;
  border: none;
  background: none;
  padding: 0;
}

.navbar-burger span {
  display: block;
  height: 2px;
  width: 18px;
  background: var(--navbar-text);
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  transition: var(--transition-fast);
  border-radius: 1px;
}

.navbar-burger span:nth-child(1) {
  top: 50%;
  transform: translate(-50%, -8px);
}

.navbar-burger span:nth-child(2) {
  top: 50%;
  transform: translate(-50%, -50%);
}

.navbar-burger span:nth-child(3) {
  top: 50%;
  transform: translate(-50%, 6px);
}

.navbar-burger:hover span {
  background: var(--color-primary);
}

.navbar-burger.is-active span:nth-child(1) {
  transform: translate(-50%, -50%) rotate(45deg);
}

.navbar-burger.is-active span:nth-child(2) {
  opacity: 0;
}

.navbar-burger.is-active span:nth-child(3) {
  transform: translate(-50%, -50%) rotate(-45deg);
}

/* Sidebar Navigation */
.sidebar {
  background: var(--sidebar-background);
  border-right: 1px solid var(--color-border);
  height: 100vh;
  overflow-y: auto;
  position: fixed;
  left: 0;
  top: 0;
  width: 250px;
  z-index: var(--z-sticky);
  transform: translateX(-100%);
  transition: var(--transition-normal);
}

/* App Sidebar (component-specific classes from Sidebar.vue) */
.app-sidebar {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--color-surface);
  border-right: 1px solid var(--color-border);
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--color-border);
  min-height: 60px;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  flex: 1;
}

.logo-icon {
  width: 32px;
  height: 32px;
  object-fit: contain;
  flex-shrink: 0;
}

.logo-text {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  white-space: nowrap;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: var(--color-border) var(--color-surface);
}

.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: var(--color-surface);
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-hover);
}

.sidebar-brand .title {
  margin: 0;
  color: var(--color-text);
}

.sidebar-toggle,
.sidebar-close {
  border: none;
  background: transparent;
  border-radius: var(--radius);
  transition: var(--transition-fast);
  color: var(--color-text-muted);
}

.sidebar-toggle:hover,
.sidebar-close:hover {
  background: var(--color-surface-hover);
  color: var(--color-text);
}

.sidebar-section {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--color-border);
}

.sidebar-section:last-child {
  border-bottom: none;
}

.sidebar-section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-3);
}

.sidebar-section-header .button {
  border-radius: var(--radius);
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  color: var(--color-text-muted);
  transition: var(--transition-fast);
  padding: 0.375rem 0.5rem;
}

.sidebar-section-header .button:hover {
  background: var(--color-surface-hover);
  border-color: var(--color-primary);
  color: var(--color-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.sidebar-section-header .header-actions {
  display: flex;
  gap: 0.25rem;
}

.section-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-muted);
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
  margin: 0;
}

/* User Profile */
.user-section {
  background: var(--card-background);
  border-radius: var(--radius-lg);
  margin: var(--spacing-4);
  padding: var(--spacing-4);
  border-bottom: none;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.user-profile.is-collapsed {
  justify-content: center;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-email {
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Navigation Menu */
.sidebar-menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

.menu-item {
  margin-bottom: var(--spacing-1);
}

.menu-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  border-radius: var(--radius);
  color: var(--color-text-muted);
  text-decoration: none;
  transition: var(--transition-fast);
  cursor: pointer;
}

.menu-link:hover {
  background: var(--color-surface-hover);
  color: var(--color-text);
}

.menu-link.is-active {
  background: var(--color-primary);
  color: white;
}

.menu-text {
  flex: 1;
  font-weight: var(--font-weight-medium);
}

.menu-count {
  font-size: var(--font-size-xs);
  background: rgba(0, 0, 0, 0.1);
  color: inherit;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  min-width: 1.5rem;
  text-align: center;
}

.menu-link.is-active .menu-count {
  background: rgba(255, 255, 255, 0.2);
}

/* Tags */
.tags-container {
}

.tags-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.filter-mode {
  display: flex;
  justify-content: center;
}

.filter-mode .field.has-addons {
  margin-bottom: 0;
}

.filter-mode .button {
  font-size: var(--font-size-xs);
  padding: 0.25rem 0.5rem;
}

.filter-indicator {
  color: var(--color-primary);
  font-weight: var(--font-weight-semibold);
}

.header-actions {
  display: flex;
  gap: 0.25rem;
}

.empty-state {
  text-align: center;
  padding: 1.5rem 0;
}

.empty-state .button {
  margin-top: 0.5rem;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.375rem;
}

.tag.is-clickable {
  cursor: pointer;
  transition: var(--transition-fast);
  border: 1px solid var(--color-border);
  background: var(--color-background);
  color: var(--color-text);
  display: flex;
  align-items: center;
  gap: 0.2rem;
  padding: 0.25rem 0.375rem;
  font-size: var(--font-size-xs);
}

.tag.is-clickable:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
  border-color: var(--tag-color, var(--color-primary));
}

.tag.is-clickable.is-primary {
  color: white;
  border-color: var(--tag-color, var(--color-primary));
  background-color: var(--tag-color, var(--color-primary));
}

.tag.is-clickable.is-predefined {
  font-weight: var(--font-weight-medium);
}

.tag.is-clickable .icon {
  font-size: 0.75rem;
}

.tag-name {
  text-transform: capitalize;
  font-weight: inherit;
}

.tag-count {
  font-size: 0.65rem;
  opacity: 0.8;
  background: rgba(0, 0, 0, 0.1);
  padding: 0.1rem 0.25rem;
  border-radius: 8px;
  min-width: 1rem;
  text-align: center;
}

.tag.is-clickable.is-primary .tag-count {
  background: rgba(255, 255, 255, 0.2);
}

.button.is-primary.is-fullwidth {
  background: var(--color-primary);
  color: var(--color-text-inverse);
}

/* Quick Actions */
.actions-section {
  border-bottom: 1px solid var(--color-border);
  margin: 0 1rem;
  padding: 0.75rem;
  background: transparent;
  border-radius: var(--radius-lg);
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Collapsed State */
.app-sidebar.is-collapsed {
  width: 60px;
}

/* Collapsed sidebar */
.sidebar-collapsed .sidebar-panel {
  background: transparent;
}
/* Collapsed sidebar menu link states - consolidated */
.app-sidebar.is-collapsed .menu-link,
.app-sidebar.is-collapsed .menu-link:hover,
.app-sidebar.is-collapsed .menu-link:focus,
.app-sidebar.is-collapsed .menu-link:active {
  background: transparent;
  color: var(--color-text-muted);
  outline: none;
}

.app-sidebar.is-collapsed .menu-link.is-active,
.app-sidebar.is-collapsed .menu-link.is-active:hover {
  background: transparent;
  color: var(--color-primary);
}

/* Remove hover effects for sidebar toggle/close buttons in collapsed state */
.app-sidebar.is-collapsed .sidebar-toggle:hover,
.app-sidebar.is-collapsed .sidebar-close:hover {
  background: transparent;
  color: var(--color-text-muted);
}

/* Make main sidebar background transparent in collapsed state */
.app-sidebar.is-collapsed {
  background: transparent;
  border-right: none;
}

/* Make user section background transparent in collapsed state */
.app-sidebar.is-collapsed .user-section {
  background: transparent;
  border: none;
  margin: 0.5rem;
  padding: 0.5rem;
}

/* Make actions section background transparent in collapsed state */
.app-sidebar.is-collapsed .actions-section {
  background: transparent;
  border: none;
  margin: 0.5rem;
  padding: 0.5rem;
}

/* Make admin section background transparent in collapsed state */
.app-sidebar.is-collapsed .admin-section {
  background: transparent;
  border: none;
}

/* Make groups section background transparent in collapsed state */
.app-sidebar.is-collapsed .groups-section {
  background: transparent;
  border: none;
  margin: 0.5rem;
  padding: 0.5rem;
}

/* Make nav section background transparent in collapsed state */
/* This centers the nav section in the collapsed state by removing margin: 0.5rem; padding: 0.5rem;*/
.app-sidebar.is-collapsed .nav-section {
  background: transparent;
  border: none;
}

/* Collapsed sidebar button states - consolidated */
.app-sidebar.is-collapsed .button:hover,
.app-sidebar.is-collapsed .collapsed-create-button .button:hover,
.app-sidebar.is-collapsed .sidebar-section-header .button:hover {
  background: transparent;
  border-color: var(--color-border);
  color: var(--color-text-muted);
  transform: none;
  box-shadow: none;
}

/* Remove underlines from sidebar menu buttons on hover */
.sidebar-menu .button:hover,
.menu-link .button:hover,
.sidebar-section-header .button:hover {
  text-decoration: none !important;
}

/* Remove underlines from menu links on hover */
.menu-link:hover,
.menu-link:focus,
.menu-link.is-active:hover {
  text-decoration: none !important;
}

.app-sidebar.is-collapsed .menu-link.is-active .icon .button:hover {
  background: transparent !important;
  border-color: transparent !important;
  color: inherit !important;
  transform: none !important;
  box-shadow: none !important;
  text-decoration: none !important;
}

.app-sidebar.is-collapsed .menu-link {
  background: transparent !important;
  border-color: transparent !important;
  color: inherit !important;
  box-shadow: none !important;
  text-decoration: none !important;
}

/* Special button overrides for collapsed state - consolidated */
.app-sidebar.is-collapsed .sidebar-section-header .button[title*='Create']:hover,
.app-sidebar.is-collapsed .sidebar-section-header .button[title*='Add']:hover,
.app-sidebar.is-collapsed .collapsed-create-button .button[title*='Create']:hover,
.app-sidebar.is-collapsed .sidebar-section-header .button[title*='settings']:hover,
.app-sidebar.is-collapsed .sidebar-section-header .button[title*='Settings']:hover,
.app-sidebar.is-collapsed .collapsed-create-button .button[title*='Settings']:hover,
.app-sidebar.is-collapsed .button.is-ghost:hover {
  background: transparent;
  border-color: var(--color-border);
  color: var(--color-text-muted);
  transform: none;
  box-shadow: none;
}

/* Ensure icons have good contrast in collapsed state */
.app-sidebar.is-collapsed .menu-link .icon {
  margin: 0 auto;
  color: var(--color-text-muted);
  opacity: 0.8;
}

.app-sidebar.is-collapsed .menu-link:hover .icon {
  color: var(--color-text);
  opacity: 1;
}

.app-sidebar.is-collapsed .menu-link.is-active .icon {
  color: var(--color-primary);
  opacity: 1;
}

.app-sidebar.is-collapsed .user-avatar .icon {
  color: var(--color-text-muted);
  opacity: 0.8;
}

.app-sidebar.is-collapsed .quick-actions .button .icon {
  color: var(--color-text-muted);
  opacity: 0.8;
}

.app-sidebar.is-collapsed .quick-actions .button:hover .icon {
  color: var(--color-text);
  opacity: 1;
}

.collapsed-create-button {
  display: flex;
  justify-content: center;
  padding: 0.5rem;
}

.collapsed-create-button .button {
  border-radius: var(--radius);
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  color: var(--color-text-muted);
  transition: var(--transition-fast);
}

.collapsed-create-button .button:hover {
  background: var(--color-surface-hover);
  border-color: var(--color-primary);
  color: var(--color-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.app-sidebar.is-collapsed .logo-section {
  justify-content: center;
}

.app-sidebar.is-collapsed .logo-text {
  display: none;
}

.app-sidebar.is-collapsed .header-content {
  justify-content: center;
}

.app-sidebar.is-collapsed .header-actions {
  display: none;
}

.app-sidebar.is-collapsed .sidebar-section {
  padding: 0.5rem;
}

.app-sidebar.is-collapsed .menu-link {
  justify-content: center;
  padding: 0.5rem;
}

.app-sidebar.is-collapsed .menu-link .icon {
  margin: 0 auto;
}

.app-sidebar.is-collapsed .user-profile {
  flex-direction: column;
  gap: 0.5rem;
}

.app-sidebar.is-collapsed .quick-actions .button {
  padding: 0.75rem 0.5rem;
}

/* Dropdown z-index fixes */
.dropdown {
  position: relative;
}

.dropdown-menu {
  z-index: 9999 !important;
  position: absolute !important;
  top: 100% !important;
  min-width: 200px;
  box-shadow: var(--shadow-lg) !important;
  border: 1px solid var(--color-border);
}

.dropdown.is-active .dropdown-menu {
  display: block;
  z-index: 9999 !important;
}

.dropdown.is-right .dropdown-menu {
  right: 0 !important;
  left: auto !important;
}

/* Ensure user section doesn't interfere with dropdown */
.user-section {
  position: relative;
  z-index: 100;
}

.user-actions {
  position: relative;
  z-index: 1000;
}

/* Admin Dropdown Item */
.dropdown-item[href='/admin'] {
  color: var(--color-danger) !important;
  font-weight: var(--font-weight-semibold);
}

.dropdown-item[href='/admin']:hover {
  background-color: var(--color-danger) !important;
  color: white !important;
}

.dropdown-item[href='/admin'] .icon {
  color: var(--color-danger);
}

.dropdown-item[href='/admin']:hover .icon {
  color: white;
}

/* Responsive */
@media screen and (max-width: 768px) {
  .app-sidebar {
    width: 100%;
  }
  .sidebar-header {
    padding: 0.75rem 1rem;
  }
  .logo-text {
    font-size: 1.1rem;
  }
  .logo-icon {
    width: 28px;
    height: 28px;
  }
}

@media screen and (max-width: 480px) {
  .sidebar-header {
    padding: 0.5rem 0.75rem;
  }
  .logo-text {
    font-size: 1rem;
  }
  .logo-icon {
    width: 24px;
    height: 24px;
  }
}

.sidebar.is-active {
  transform: translateX(0);
}

.sidebar-menu {
  padding: var(--spacing-4);
}

.sidebar-item {
  display: block;
  padding: var(--spacing-2) var(--spacing-3);
  color: var(--sidebar-text);
  text-decoration: none;
  border-radius: var(--radius);
  margin-bottom: var(--spacing-1);
  transition: var(--transition-fast);
}

.sidebar-item:hover {
  background: var(--sidebar-item-hover);
  color: var(--color-primary);
}

.sidebar-item.is-active {
  background: var(--color-primary);
  color: white;
}

.sidebar-item .icon {
  margin-right: var(--spacing-2);
  width: 1.25rem;
  text-align: center;
}

/* Sidebar Header */
.sidebar-header {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--color-border);
  background: var(--color-surface);
}

.sidebar-title {
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
  margin: 0;
  color: var(--sidebar-text);
}

/* Sidebar Footer */
.sidebar-footer {
  padding: var(--spacing-4);
  border-top: 1px solid var(--color-border);
  margin-top: auto;
}

/* Breadcrumb Navigation */
.breadcrumb {
  display: flex;
  align-items: center;
  padding: var(--spacing-2) 0;
  font-size: var(--font-size-sm);
}

.breadcrumb ul {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
}

.breadcrumb li {
  display: flex;
  align-items: center;
}

.breadcrumb li + li::before {
  content: '/';
  margin: 0 var(--spacing-2);
  color: var(--color-text-muted);
}

.breadcrumb a {
  color: var(--color-primary);
  text-decoration: none;
  transition: var(--transition-fast);
}

.breadcrumb a:hover {
  color: var(--color-primary-dark);
  text-decoration: underline;
}

.breadcrumb li.is-active {
  color: var(--color-text-muted);
}

/* Tabs Navigation */
.tabs {
  display: flex;
  border-bottom: 1px solid var(--color-border);
  overflow-x: auto;
}

.tabs ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.tabs li {
  margin-bottom: -1px;
}

.tabs a {
  display: block;
  padding: var(--spacing-3) var(--spacing-4);
  color: var(--color-text-muted);
  text-decoration: none;
  border-bottom: 2px solid transparent;
  transition: var(--transition-fast);
  white-space: nowrap;
}

.tabs a:hover {
  color: var(--color-text);
  border-bottom-color: var(--color-border-hover);
}

.tabs li.is-active a {
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
}

.tabs.is-centered ul {
  justify-content: center;
}

.tabs.is-right ul {
  justify-content: flex-end;
}

.tabs.is-boxed a {
  border: 1px solid transparent;
  border-bottom: none;
  border-top-left-radius: var(--radius);
  border-top-right-radius: var(--radius);
}

.tabs.is-boxed a:hover {
  background: var(--color-surface-hover);
  border-color: var(--color-border);
}

.tabs.is-boxed li.is-active a {
  background: var(--color-background);
  border-color: var(--color-border);
  border-bottom-color: var(--color-background);
}

/* Responsive Navigation */
@media screen and (max-width: 1023px) {
  .navbar-burger {
    display: block;
  }

  .navbar-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--navbar-background);
    border-top: 1px solid var(--color-border);
    box-shadow: var(--shadow-lg);
    flex-direction: column;
    align-items: stretch;
    padding: var(--spacing-2);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition-fast);
  }

  .navbar-menu.is-active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  .navbar-start,
  .navbar-end {
    flex-direction: column;
    margin: 0;
  }

  .navbar-item {
    justify-content: flex-start;
    width: 100%;
    margin-bottom: var(--spacing-1);
  }

  .navbar-dropdown {
    position: static;
    opacity: 1;
    visibility: visible;
    transform: none;
    box-shadow: none;
    border: none;
    background: var(--color-surface);
    margin-top: var(--spacing-1);
    border-radius: var(--radius);
  }

  .sidebar {
    width: 100%;
    height: 100vh;
    z-index: var(--z-modal);
  }
}

@media screen and (max-width: 768px) {
  .navbar {
    padding: var(--spacing-2) var(--spacing-3);
  }

  .navbar-brand .title {
    font-size: var(--font-size-lg);
  }

  .tabs {
    font-size: var(--font-size-sm);
  }

  .tabs a {
    padding: var(--spacing-2) var(--spacing-3);
  }

  .breadcrumb {
    font-size: var(--font-size-xs);
  }
}

/* Button styles moved to buttons.css for consolidation */
