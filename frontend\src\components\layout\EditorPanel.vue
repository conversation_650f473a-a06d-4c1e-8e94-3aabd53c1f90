<template>
  <div class="h-full flex flex-col bg-white dark:bg-gray-800" :class="{ 'fixed inset-0 z-50': isFullscreen }">
    <!-- Editor Header -->
    <div class="flex-shrink-0 p-4 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <button v-if="isFullscreen"
                  class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
                  @click="$emit('toggle-fullscreen')"
                  title="Exit fullscreen">
            <i class="fas fa-compress w-4 h-4"></i>
          </button>

          <!-- Dashboard header content when no note is selected -->
          <div v-if="!selectedNote && showDashboard" class="flex items-center space-x-3">
            <div class="flex items-center space-x-2">
              <i class="fas fa-tachometer-alt w-5 h-5 text-primary-500"></i>
              <h1 class="text-xl font-semibold text-gray-900 dark:text-white">Dashboard</h1>
            </div>
            <p class="text-sm text-gray-500 dark:text-gray-400">
              Welcome back! Here's what's happening with your notes and tasks.
            </p>
          </div>

          <!-- Note info when editing a note -->
          <div v-else-if="selectedNote && !selectedNote.isCreating && selectedNote.id">
            <h1 class="text-xl font-semibold text-gray-900 dark:text-white">
              {{ selectedNote.title || 'Untitled' }}
            </h1>
            <div class="flex items-center space-x-4 mt-1 text-sm text-gray-500 dark:text-gray-400">
              <span v-if="selectedNote.noteType" class="flex items-center space-x-1">
                <i :class="getNoteTypeIcon(selectedNote.noteType)" class="w-4 h-4"></i>
                <span>{{ getNoteTypeName(selectedNote.noteType) }}</span>
              </span>
              <span v-if="selectedNote.updatedAt">
                Last edited {{ formatDate(selectedNote.updatedAt) }}
              </span>
              <span v-else>
                New note
              </span>
            </div>
          </div>
        
          <!-- Creating note header -->
          <div v-else-if="selectedNote && selectedNote.isCreating">
            <h1 class="text-xl font-semibold text-gray-900 dark:text-white">Creating New Note</h1>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Choose a format and start writing</p>
          </div>
        </div>

        <div v-if="selectedNote && !selectedNote.isCreating && selectedNote.id" class="flex items-center space-x-2">
          <!-- Note type selector -->
          <div class="relative">
            <button class="flex items-center px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
              <i :class="getNoteTypeIcon(selectedNote.noteType)" class="w-4 h-4 mr-2"></i>
              <span>{{ getNoteTypeName(selectedNote.noteType) }}</span>
              <i class="fas fa-angle-down w-3 h-3 ml-2"></i>
            </button>
            <div class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-50 hidden">
                <a class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                   :class="{ 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-200': selectedNote.noteType === 'richtext' }"
                   @click="changeNoteType('richtext')">
                  <i class="fas fa-align-left w-4 h-4 mr-3"></i>
                  <span>Rich Text</span>
                </a>
                <a class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                   :class="{ 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-200': selectedNote.noteType === 'markdown' }"
                   @click="changeNoteType('markdown')">
                  <i class="fab fa-markdown w-4 h-4 mr-3"></i>
                  <span>Markdown</span>
                </a>
                <a class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                   :class="{ 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-200': selectedNote.noteType === 'kanban' }"
                   @click="changeNoteType('kanban')">
                  <i class="fas fa-columns w-4 h-4 mr-3"></i>
                  <span>Kanban</span>
                </a>
              </div>
            </div>
          </div>

          <!-- More actions -->
          <div class="relative">
            <button class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
                    @click="toggleMoreActionsDropdown">
              <i class="fas fa-ellipsis-h w-4 h-4"></i>
            </button>
            <div v-if="showMoreActionsDropdown"
                 class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-50">
                <a class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                   @click="toggleFavorite">
                  <i :class="selectedNote.isFavorite ? 'fas fa-star' : 'far fa-star'" class="w-4 h-4 mr-3"></i>
                  <span>{{ selectedNote.isFavorite ? 'Remove from' : 'Add to' }} Favorites</span>
                </a>
                <a class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                   @click="shareNote">
                  <i class="fas fa-share-alt w-4 h-4 mr-3"></i>
                  <span>Share</span>
                </a>
                <a class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                   @click="exportNote">
                  <i class="fas fa-download w-4 h-4 mr-3"></i>
                  <span>Export</span>
                </a>
                <a class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                   @click="viewHistory">
                  <i class="fas fa-history w-4 h-4 mr-3"></i>
                  <span>Version History</span>
                </a>
                <hr class="my-1 border-gray-200 dark:border-gray-600">
                <a class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                   @click="duplicateNote">
                  <i class="fas fa-copy w-4 h-4 mr-3"></i>
                  <span>Duplicate</span>
                </a>
                <a class="flex items-center px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                   @click="deleteNote">
                  <i class="fas fa-trash w-4 h-4 mr-3"></i>
                  <span>Delete</span>
                </a>
              </div>
            </div>
          </div>

          <!-- Fullscreen toggle -->
          <button v-if="!isFullscreen"
                  class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
                  @click="$emit('toggle-fullscreen')"
                  title="Enter fullscreen">
            <i class="fas fa-expand w-4 h-4"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Editor Content -->
    <div class="flex-1 overflow-y-auto">
      <!-- Dashboard State -->
      <div v-if="!selectedNote && showDashboard" class="h-full">
        <DashboardOverview @create-note="createNote" @create-group="$emit('create-group')"
          @open-search="$emit('open-search')" @open-settings="$emit('open-settings')" />
      </div>

      <!-- Welcome State -->
      <div v-else-if="!selectedNote" class="flex items-center justify-center h-full">
        <div class="text-center">
          <div class="text-6xl text-gray-300 dark:text-gray-600 mb-4">
            <i class="fas fa-edit"></i>
          </div>
          <h2 class="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">Welcome to Notes</h2>
          <p class="subtitle has-text-grey">
            Select a note from the list to start editing, or create a new one.
          </p>
          <div class="welcome-actions">
            <button class="button is-primary" @click="createNote">
              <span class="icon">
                <i class="fas fa-plus"></i>
              </span>
              <span>Create New Note</span>
            </button>
          </div>

          <!-- Quick Start Tips -->
          <div class="quick-tips">
            <h3 class="title is-6">Quick Tips:</h3>
            <ul>
              <li>
                <span class="icon is-small">
                  <i class="fas fa-keyboard"></i>
                </span>
                Use <kbd>Ctrl+F</kbd> (or <kbd>Cmd+F</kbd>) to search notes
              </li>
              <li>
                <span class="icon is-small">
                  <i class="fas fa-save"></i>
                </span>
                Notes auto-save as you type
              </li>
              <li>
                <span class="icon is-small">
                  <i class="fas fa-users"></i>
                </span>
                Share notes with your team for collaboration
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Note Creation Mode -->
      <div v-else-if="selectedNote && selectedNote.isCreating" class="note-creation">
        <NoteEditor key="creating" ref="noteEditorRef" @saved="handleNoteSaved" @error="handleNoteError" />
      </div>

      <!-- Note Editor -->
      <div v-else-if="selectedNote && !selectedNote.isCreating && selectedNote.id" class="note-editor">
        <NoteEditor :key="selectedNote.id" :note="selectedNote" @saved="handleNoteSaved" @error="handleNoteError" />
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import NoteEditor from '../notes/NoteEditor.vue'
import DashboardOverview from '../dashboard/DashboardOverview.vue'

// Props
interface Props {
  selectedNote?: any
  isFullscreen: boolean
  showDashboard?: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'toggle-fullscreen': []
  'create-note': []
  'note-saved': [note: any]
  'share-note': [note: any]
  'create-group': []
  'open-search': []
  'open-settings': []
}>()

// Reactive state
const noteEditorRef = ref()
const showMoreActionsDropdown = ref(false)

// Methods

const toggleMoreActionsDropdown = () => {
  showMoreActionsDropdown.value = !showMoreActionsDropdown.value
}

const changeNoteType = (type: string) => {
  if (props.selectedNote && props.selectedNote.noteType !== type) {
    if (confirm('Changing note type may affect formatting. Continue?')) {
      props.selectedNote.noteType = type
    }
  }
  // Close the note type dropdown (if it was open)
}

const createNote = () => {
  // Emit event to parent to trigger note creation
  emit('create-note')
}

const toggleFavorite = () => {
  if (props.selectedNote) {
    props.selectedNote.isFavorite = !props.selectedNote.isFavorite
  }
  showMoreActionsDropdown.value = false
}

const shareNote = () => {
  if (props.selectedNote) {
    emit('share-note', props.selectedNote)
  }
  showMoreActionsDropdown.value = false
}

const exportNote = () => {
  console.log('Export note')
  showMoreActionsDropdown.value = false
}

const viewHistory = () => {
  console.log('View history')
  showMoreActionsDropdown.value = false
}

const duplicateNote = () => {
  console.log('Duplicate note')
  showMoreActionsDropdown.value = false
}

const deleteNote = () => {
  if (confirm('Are you sure you want to delete this note?')) {
    console.log('Delete note')
  }
  showMoreActionsDropdown.value = false
}

const handleNoteSaved = (note: any) => {
  // Emit event to parent to update the selected note
  emit('note-saved', note)
}

const handleNoteError = (error: string) => {
  console.error('Note error:', error)
  // Could show a notification here
}

// Watch for creation mode and initialize the editor
watch(
  () => props.selectedNote?.isCreating,
  async (isCreating, oldIsCreating) => {
    if (isCreating && !oldIsCreating) {
      await nextTick()
      try {
        if (noteEditorRef.value && typeof noteEditorRef.value.startCreating === 'function') {
          noteEditorRef.value.startCreating()
        }
      } catch (error) {
        console.error('Error starting note creation:', error)
      }
    }
  }
)

// Utility methods
const getNoteTypeIcon = (type: string) => {
  switch (type) {
    case 'richtext': return 'fas fa-align-left'
    case 'markdown': return 'fab fa-markdown'
    case 'kanban': return 'fas fa-columns'
    default: return 'fas fa-file-alt'
  }
}

const getNoteTypeClass = (type: string) => {
  switch (type) {
    case 'richtext': return 'has-text-info'
    case 'markdown': return 'has-text-success'
    case 'kanban': return 'has-text-warning'
    default: return 'has-text-grey'
  }
}

const getNoteTypeName = (type: string) => {
  switch (type) {
    case 'richtext': return 'Rich Text'
    case 'markdown': return 'Markdown'
    case 'kanban': return 'Kanban'
    default: return 'Note'
  }
}



const formatDate = (date: string | Date) => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const now = new Date()
  const diffMs = now.getTime() - dateObj.getTime()
  const diffMinutes = Math.floor(diffMs / (1000 * 60))

  if (diffMinutes < 1) {
    return 'just now'
  } else if (diffMinutes < 60) {
    return `${diffMinutes} minutes ago`
  } else if (diffMinutes < 1440) {
    const hours = Math.floor(diffMinutes / 60)
    return `${hours} hour${hours > 1 ? 's' : ''} ago`
  } else {
    return dateObj.toLocaleDateString()
  }
}

// Click outside handler to close dropdown
const handleClickOutside = (event: Event) => {
  const target = event.target as Element
  if (!target.closest('.dropdown')) {
    showMoreActionsDropdown.value = false
  }
}

// Lifecycle hooks
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.editor-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--color-background);
}

.editor-panel.is-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid var(--color-border);
  background: var(--card-header-background);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
  min-width: 0;
}

.note-info {
  flex: 1;
  min-width: 0;
}

.dashboard-header-content {
  flex: 1;
  min-width: 0;
}

.dashboard-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-text-strong);
  margin: 0 0 0.25rem 0;
}

.dashboard-title .icon {
  color: #007bff;
}

.dashboard-subtitle {
  font-size: 0.875rem;
  color: #6c757d;
  margin: 0;
  line-height: 1.4;
}

.note-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--color-text-strong);
  margin: 0 0 0.25rem 0;
  border: none;
  background: transparent;
  outline: none;
  cursor: text;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.note-title:focus {
  background: var(--color-surface);
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  margin: -0.25rem -0.5rem 0.25rem -0.5rem;
}

.note-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.875rem;
}

.note-type {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-weight: 500;
}

.note-date {
  color: #6c757d;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.editor-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Dropdown width control */
.dropdown.is-right .dropdown-menu {
  min-width: 200px;
  max-width: 300px;
  width: auto;
  right: 0;
  left: auto;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e9ecef;
  border-radius: 6px;
}

.dropdown-content {
  white-space: nowrap;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  min-height: 2.5rem;
}

.dropdown-item .icon {
  flex-shrink: 0;
  width: 1rem;
  text-align: center;
}

.dropdown-item span:last-child {
  flex: 1;
  min-width: 0;
}



.editor-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dashboard-state {
  height: 100%;
  overflow-y: auto;
}

.welcome-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;
}

.welcome-content {
  text-align: center;
  max-width: 400px;
}

.welcome-actions {
  margin: 2rem 0;
}

.quick-tips {
  text-align: left;
  margin-top: 2rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.quick-tips ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.quick-tips li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.quick-tips kbd {
  background: #e9ecef;
  border: 1px solid #adb5bd;
  border-radius: 3px;
  padding: 0.125rem 0.25rem;
  font-size: 0.75rem;
}

.note-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.editor-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.editor-toolbar {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
  flex-wrap: wrap;
}

.toolbar-group {
  display: flex;
  gap: 0.25rem;
  padding-right: 0.5rem;
  border-right: 1px solid #dee2e6;
}

.toolbar-group:last-child {
  border-right: none;
}

.editor-area {
  flex: 1;
  padding: 1rem;
  outline: none;
  overflow-y: auto;
  line-height: 1.6;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.markdown-panels {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.markdown-input-panel,
.markdown-preview-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.markdown-input-panel {
  border-right: 1px solid #e9ecef;
}

.editor-textarea {
  flex: 1;
  border: none;
  border-radius: 0;
  resize: none;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.6;
}

.preview-header {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.preview-content {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  line-height: 1.6;
}

.kanban-editor {
  padding: 1rem;
}

.kanban-toolbar {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.kanban-board {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  border: 2px dashed #e9ecef;
  border-radius: 8px;
}

.kanban-placeholder {
  text-align: center;
}

.tags-panel {
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  padding: 1rem;
}

.tags-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.tags-header .title {
  margin: 0;
}

.tag-input-container {
  margin-bottom: 0.75rem;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.empty-tags {
  text-align: center;
  padding: 1rem 0;
}

/* Responsive */
@media screen and (max-width: 768px) {
  .editor-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .header-left,
  .header-right {
    justify-content: space-between;
  }

  .editor-actions {
    justify-content: flex-end;
  }

  .markdown-panels {
    flex-direction: column;
  }

  .markdown-input-panel {
    border-right: none;
    border-bottom: 1px solid #e9ecef;
  }

  .toolbar-group {
    flex-wrap: wrap;
  }

  /* Mobile dropdown adjustments */
  .dropdown.is-right .dropdown-menu {
    min-width: 180px;
    max-width: 250px;
    right: 0;
    left: auto;
  }
}
</style>