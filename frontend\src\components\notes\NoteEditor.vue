<template>
  <div class="note-editor">
    <!-- Auto-save status indicator -->
    <div class="auto-save-status" v-if="showAutoSaveStatus">
      <span class="status-indicator" :class="{
        'is-saving': notesStore.autoSaveStatus === 'saving',
        'is-saved': notesStore.autoSaveStatus === 'saved',
        'is-error': notesStore.autoSaveStatus === 'error'
      }">
        <template v-if="notesStore.autoSaveStatus === 'saving'">
          <i class="fas fa-spinner fa-spin"></i> Saving...
        </template>
        <template v-else-if="notesStore.autoSaveStatus === 'saved'">
          <i class="fas fa-check"></i> Saved
        </template>
        <template v-else-if="notesStore.autoSaveStatus === 'error'">
          <i class="fas fa-exclamation-triangle"></i> Save failed
        </template>
      </span>
    </div>

    <!-- Offline indicator -->
    <div class="offline-indicator" v-if="!offlineSync.isOnline.value">
      <span class="tag is-warning">
        <i class="fas fa-wifi-slash"></i>
        Offline
        <span v-if="offlineSync.queuedOperations.value > 0">
          ({{ offlineSync.queuedOperations.value }} pending)
        </span>
      </span>
    </div>

    <!-- Note title -->
    <div class="field">
      <div class="control">
        <input v-model="localTitle" @input="onTitleChange" class="input is-large" type="text"
          placeholder="Note title..." :disabled="!note && !isCreating" />
      </div>
    </div>

    <!-- Note type selector -->
    <div class="field" v-if="!note">
      <label class="label">Note Type</label>
      <div class="control">
        <div class="select">
          <select v-model="selectedNoteType">
            <option value="markdown">Markdown</option>
            <option value="richtext">Rich Text</option>
            <option value="kanban">Kanban Board</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Content editor based on note type -->
    <div class="editor-container">
      <!-- Markdown Editor -->
      <div v-if="currentNoteType === 'markdown'" class="markdown-editor">
        <MarkdownEditor v-model="localContent" @change="onContentChange" :disabled="!note && !isCreating"
          placeholder="Start writing your markdown..." />
      </div>

      <!-- Rich Text Editor -->
      <div v-else-if="currentNoteType === 'richtext'" class="richtext-editor">
        <RichTextEditor v-model="localContent" @change="onContentChange" :disabled="!note && !isCreating"
          placeholder="Start writing your rich text note..." />
      </div>

      <!-- Kanban Editor -->
      <div v-else-if="currentNoteType === 'kanban'" class="kanban-editor">
        <KanbanBoardInline v-model="localContent" @change="onContentChange" :disabled="!note && !isCreating"
          placeholder="Start building your kanban board..." />
      </div>
    </div>

    <!-- Tags -->
    <div class="field">
      <TagInput v-model="localTagObjects" :suggestions="tagSuggestions" label="Tags" placeholder="Add tags..."
        :allow-create="true" :max-tags="10" @tag-created="handleTagCreated" @tag-added="handleTagAdded"
        @tag-removed="handleTagRemoved" />
      
      <!-- Premade Tag Pills -->
      <div class="premade-tags" v-if="availablePremadeTags.length > 0">
        <label class="label is-small">Quick Tags:</label>
        <div class="tag-pills">
          <button
            v-for="tag in availablePremadeTags"
            :key="tag.id"
            @click="togglePremadeTag(tag)"
            class="tag-pill"
            :class="{ 'is-selected': isTagSelected(tag.name) }"
            :style="{ '--tag-color': tag.color }"
          >
            <span class="icon is-small">
              <i :class="tag.icon"></i>
            </span>
            <span>{{ tag.name }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Action buttons -->
    <div class="action-buttons">
      <div class="action-group primary-actions">
        <button @click="saveNote" class="button is-primary action-button"
          :class="{ 'is-loading': notesStore.isLoading }" :disabled="!hasChanges">
          <span class="icon">
            <i class="fas fa-save"></i>
          </span>
          <span>{{ note ? 'Save Changes' : 'Create Note' }}</span>
        </button>

        <button v-if="note" @click="cancelChanges" class="button action-button" :disabled="!hasChanges">
          Cancel
        </button>
      </div>

      <div class="action-group secondary-actions" v-if="note">
        <button @click="openExportModal" class="button is-info is-outlined action-button">
          <span class="icon">
            <i class="fas fa-download"></i>
          </span>
          <span>Export</span>
        </button>

        <button @click="deleteNote" class="button is-danger is-outlined action-button"
          :class="{ 'is-loading': notesStore.isLoading }">
          <span class="icon">
            <i class="fas fa-trash"></i>
          </span>
          <span>Delete</span>
        </button>
      </div>
    </div>

    <!-- Export Modal -->
    <ExportModal :is-open="showExportModal" :note-ids="note ? [note.id] : []" :note-title="note?.title"
      @close="showExportModal = false" @exported="onExported" />

    <!-- Auto-save settings -->
    <div class="auto-save-settings" v-if="showSettings">
      <div class="field">
        <label class="checkbox">
          <input type="checkbox" v-model="notesStore.autoSaveEnabled" />
          Enable auto-save
        </label>
      </div>

      <div class="field" v-if="notesStore.autoSaveEnabled">
        <label class="label">Auto-save interval (seconds)</label>
        <div class="control">
          <input v-model.number="autoSaveSeconds" @change="updateAutoSaveInterval" class="input" type="number" min="1"
            max="60" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useNotesStore } from '../../stores/notes'
import { useTagsStore } from '../../stores/tags'
import { useAutoSave, useOfflineSync } from '../../composables/useAutoSave'
import type { Note, CreateNoteData, Tag } from '../../services/noteService'
import RichTextEditor from '../editors/RichTextEditor.vue'
import MarkdownEditor from '../editors/MarkdownEditor.vue'
import KanbanBoardInline from '../editors/KanbanBoardInline.vue'
import TagInput from '../tags/TagInput.vue'
import ExportModal from '../export/ExportModal.vue'

interface Props {
  note?: Note | null
  showAutoSaveStatus?: boolean
  showSettings?: boolean
}

interface Emits {
  (e: 'saved', note: Note): void
  (e: 'deleted', noteId: string): void
  (e: 'error', error: string): void
}

const props = withDefaults(defineProps<Props>(), {
  showAutoSaveStatus: true,
  showSettings: false
})

const emit = defineEmits<Emits>()

const notesStore = useNotesStore()
const tagsStore = useTagsStore()
const offlineSync = useOfflineSync()

// Local state
const localTitle = ref('')
const localContent = ref('')
const localTags = ref<string[]>([])
const localTagObjects = ref<Tag[]>([])
const selectedNoteType = ref<'richtext' | 'markdown' | 'kanban'>('markdown')
const isCreating = ref(false)

// Auto-save settings
const autoSaveSeconds = ref(Math.floor(notesStore.autoSaveInterval / 1000))

// Export modal state
const showExportModal = ref(false)

// Computed
const currentNoteType = computed(() => {
  return props.note?.noteType || selectedNoteType.value
})

const hasChanges = computed(() => {
  if (!props.note && !isCreating.value) return false

  if (props.note) {
    return (
      localTitle.value !== props.note.title ||
      localContent.value !== props.note.content ||
      JSON.stringify(localTagObjects.value.map(t => t.name).sort()) !== JSON.stringify(props.note.tags.map(t => t.name).sort())
    )
  }

  return localTitle.value.trim() !== '' || localContent.value.trim() !== ''
})

// Convert tags store tags to TagInput format
const tagSuggestions = computed(() => {
  return tagsStore.availableTags.map(storeTag => ({
    id: storeTag.id,
    name: storeTag.name,
    userId: '',
    createdAt: new Date(storeTag.createdAt),
    updatedAt: new Date(storeTag.updatedAt),
    isPredefined: storeTag.isPredefined || false,
    count: storeTag.count || 0,
    icon: storeTag.icon || 'fas fa-tag',
    color: storeTag.color || '#6c757d'
  }))
})

// Get available premade tags for quick selection
const availablePremadeTags = computed(() => {
  return tagsStore.predefinedTags.slice(0, 8) // Show max 8 premade tags
})

// Auto-save composable - only initialize for existing notes with valid IDs
const autoSave = (props.note && props.note.id && typeof props.note.id === 'string' && props.note.id !== 'null') ? useAutoSave(props.note.id, {
  onSave: (data) => {
    console.log('Auto-saved:', data)
  },
  onError: (error) => {
    emit('error', error.message)
  }
}) : null

// Methods
const onTitleChange = () => {
  if (autoSave && props.note) {
    autoSave.updateTitle(localTitle.value)
  }
}

const onContentChange = () => {
  if (autoSave && props.note) {
    autoSave.updateContent(localContent.value)
  }
}

const handleTagCreated = async (tagName: string) => {
  try {
    // Create tag in the tags store
    const newTag = await tagsStore.createTag({ 
      name: tagName,
      icon: 'fas fa-tag',
      color: '#6c757d'
    })
    console.log('Tag created in store:', newTag)
  } catch (error) {
    console.error('Failed to create tag:', error)
    emit('error', error instanceof Error ? error.message : 'Failed to create tag')
  }
}

const handleTagAdded = (tag: Tag) => {
  // Update tag count in store when tag is added to a note
  const storeTag = tagsStore.getTagByName(tag.name)
  if (storeTag) {
    storeTag.count = (storeTag.count || 0) + 1
  }
  
  if (autoSave && props.note) {
    autoSave.updateTags(localTagObjects.value.map(t => t.name))
  }
}

const handleTagRemoved = (tag: Tag, index: number) => {
  // Update tag count in store when tag is removed from a note
  const storeTag = tagsStore.getTagByName(tag.name)
  if (storeTag && storeTag.count > 0) {
    storeTag.count = storeTag.count - 1
  }
  
  if (autoSave && props.note) {
    autoSave.updateTags(localTagObjects.value.map(t => t.name))
  }
}

const isTagSelected = (tagName: string) => {
  return localTagObjects.value.some(tag => tag.name === tagName)
}

const togglePremadeTag = (storeTag: any) => {
  const isSelected = isTagSelected(storeTag.name)
  
  if (isSelected) {
    // Remove tag
    const index = localTagObjects.value.findIndex(tag => tag.name === storeTag.name)
    if (index > -1) {
      const removedTag = localTagObjects.value[index]
      localTagObjects.value.splice(index, 1)
      handleTagRemoved(removedTag, index)
    }
  } else {
    // Add tag
    const newTag: Tag = {
      id: storeTag.id,
      name: storeTag.name,
      userId: '',
      createdAt: storeTag.createdAt.toISOString(),
      // count: storeTag.count || 0, // Removed as 'count' is not in Tag interface
      icon: storeTag.icon,
      color: storeTag.color
    }
    localTagObjects.value.push(newTag)
    handleTagAdded(newTag)
  }
}

const saveNote = async () => {
  try {
    if (props.note) {
      // Update existing note
      if (autoSave) {
        await autoSave.forceSave()
      } else {
        const updatedNote = await notesStore.updateNote(props.note.id, {
          title: localTitle.value,
          content: localContent.value,
          tags: localTagObjects.value.map(t => t.name)
        })
        
        // Tag counts are automatically updated by the notes store
        
        emit('saved', updatedNote)
      }
    } else {
      // Create new note
      const noteData: CreateNoteData = {
        title: localTitle.value || 'Untitled Note',
        content: localContent.value,
        noteType: selectedNoteType.value,
        tags: localTagObjects.value.map(t => t.name)
      }

      const newNote = await notesStore.createNote(noteData)
      
      // Tag counts are automatically updated by the notes store
      
      emit('saved', newNote)

      // Reset form
      localTitle.value = ''
      localContent.value = ''
      localTagObjects.value = []
      isCreating.value = false
    }
  } catch (error) {
    emit('error', error instanceof Error ? error.message : 'Failed to save note')
  }
}



const cancelChanges = () => {
  if (props.note) {
    localTitle.value = props.note.title
    localContent.value = props.note.content
    localTagObjects.value = [...props.note.tags]

    if (autoSave) {
      autoSave.cancelAutoSave()
    }
  }
}

const deleteNote = async () => {
  if (props.note && confirm('Are you sure you want to delete this note?')) {
    try {
      await notesStore.deleteNote(props.note.id)
      emit('deleted', props.note.id)
    } catch (error) {
      emit('error', error instanceof Error ? error.message : 'Failed to delete note')
    }
  }
}

const openExportModal = () => {
  showExportModal.value = true
}

const onExported = (result: { success: boolean; filename?: string; error?: string }) => {
  if (result.success) {
    // You might want to show a success toast here
    console.log('Note exported successfully:', result.filename)
  } else {
    emit('error', result.error || 'Export failed')
  }
}

const updateAutoSaveInterval = () => {
  notesStore.setAutoSaveInterval(autoSaveSeconds.value * 1000)
}

const startCreating = () => {
  isCreating.value = true
}

// Initialize local state when note changes
watch(
  () => props.note,
  async (newNote) => {
    if (newNote && newNote.id) {
      localTitle.value = newNote.title || ''
      localContent.value = newNote.content || ''

      console.log('NoteEditor: initialized localContent:', {
        contentLength: localContent.value.length,
        contentPreview: localContent.value.substring(0, 100)
      })

      // Handle tags safely - they should be Tag objects
      if (Array.isArray(newNote.tags)) {
        localTagObjects.value = newNote.tags.map(t =>
          typeof t === 'string' ? { id: '', name: t, userId: '', createdAt: '' } : t
        )
        
        // Ensure all tags from this note exist in the tags store
        for (const tag of newNote.tags) {
          const tagName = typeof tag === 'string' ? tag : tag.name
          const existingTag = tagsStore.getTagByName(tagName)
          if (!existingTag) {
            try {
              await tagsStore.createTag({
                name: tagName,
                icon: 'fas fa-tag',
                color: '#6c757d'
              })
            } catch (error) {
              console.warn('Failed to create tag in store:', tagName, error)
            }
          }
        }
      } else {
        localTagObjects.value = []
      }

      if (autoSave) {
        autoSave.initialize(newNote.content || '')
      }
    } else {
      console.log('NoteEditor: clearing local state (no note or no ID)')
      localTitle.value = ''
      localContent.value = ''
      localTagObjects.value = []
      isCreating.value = false
    }
  },
  { immediate: true }
)

// Keyboard shortcuts
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl+S or Cmd+S to save
  if ((event.ctrlKey || event.metaKey) && event.key === 's') {
    event.preventDefault()
    saveNote()
  }
}

onMounted(async () => {
  document.addEventListener('keydown', handleKeydown)

  // Initialize tags store if not already done
  try {
    if (tagsStore.availableTags.length === 0) {
      await tagsStore.initialize()
    }
  } catch (error) {
    console.error('Failed to initialize tags store:', error)
  }
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})

// Expose methods for parent components
defineExpose({
  startCreating,
  saveNote,
  cancelChanges
})
</script>

<style scoped>
.note-editor {
  margin: 0 auto;
  padding: 1rem;
}

.auto-save-status {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1000;
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-indicator.is-saving {
  background-color: #3273dc;
  color: white;
}

.status-indicator.is-saved {
  background-color: #23d160;
  color: white;
}

.status-indicator.is-error {
  background-color: #ff3860;
  color: white;
}

.offline-indicator {
  position: fixed;
  top: 4rem;
  right: 1rem;
  z-index: 1000;
}

.editor-container {
  margin: 1rem 0;
}

.textarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
}



.auto-save-settings {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #dbdbdb;
}

/* Action Buttons Styling */
.action-buttons {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  margin-top: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.action-group {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.primary-actions {
  flex: 1;
}

.secondary-actions {
  flex-shrink: 0;
}

.action-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.2s ease;
  min-height: 2.75rem;
}

.action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.action-button:active {
  transform: translateY(0);
}

.action-button .icon {
  font-size: 0.875rem;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .action-group {
    justify-content: center;
  }

  .primary-actions {
    order: 1;
  }

  .secondary-actions {
    order: 2;
  }

  .action-button {
    flex: 1;
    justify-content: center;
  }
}

@media screen and (max-width: 480px) {
  .action-buttons {
    padding: 0.75rem;
  }

  .action-group {
    flex-direction: column;
    width: 100%;
  }

  .action-button {
    width: 100%;
  }
}

/* Premade Tag Pills Styling */
.premade-tags {
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid var(--color-border);
}

.premade-tags .label {
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: var(--color-text-muted);
  font-weight: 500;
}

.tag-pills {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag-pill {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  border: 1px solid var(--color-border);
  border-radius: 20px;
  background: var(--color-background);
  color: var(--color-text);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.tag-pill:hover {
  border-color: var(--tag-color, #6c757d);
  color: var(--tag-color, #6c757d);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tag-pill.is-selected {
  background: var(--tag-color, #6c757d);
  border-color: var(--tag-color, #6c757d);
  color: white;
}

.tag-pill.is-selected:hover {
  background: var(--tag-color, #6c757d);
  opacity: 0.9;
}

.tag-pill .icon {
  font-size: 0.75rem;
}

/* Responsive design for tag pills */
@media screen and (max-width: 768px) {
  .tag-pills {
    gap: 0.375rem;
  }
  
  .tag-pill {
    padding: 0.25rem 0.5rem;
    font-size: 0.8125rem;
  }
  
  .tag-pill .icon {
    font-size: 0.6875rem;
  }
}
</style>