/* Tailwind Base Layer */
@tailwind base;

@layer base {
  /* CSS Custom Properties for Theme System */
  :root {
    /* Primary Colors */
    --color-primary: #00d1b2;
    --color-primary-dark: #00a896;
    --color-primary-light: #33fff3;
    --color-primary-alpha: rgba(0, 209, 178, 0.1);

    /* Semantic Colors */
    --color-link: #3273dc;
    --color-link-hover: #0284c7;
    --color-info: #3b82f6;
    --color-success: #48c774;
    --color-warning: #ffdd57;
    --color-danger: #f14668;

    /* Background Colors */
    --color-background: #ffffff;
    --color-surface: #f9fafb;
    --color-surface-hover: #f3f4f6;
    --color-card: #ffffff;
    --color-modal: #ffffff;

    /* Text Colors */
    --color-text: #374151;
    --color-text-strong: #111827;
    --color-text-muted: #6b7280;
    --color-text-light: #9ca3af;
    --color-text-inverse: #ffffff;

    /* Border Colors */
    --color-border: #d1d5db;
    --color-border-hover: #9ca3af;

    /* Component-specific colors */
    --navbar-background: #ffffff;
    --navbar-text: #374151;
    --navbar-border: #e5e7eb;

    --sidebar-background: #f9fafb;
    --sidebar-text: #374151;
    --sidebar-border: #e5e7eb;

    --button-background: #ffffff;
    --button-text: #374151;
    --button-border: #d1d5db;
    --button-hover-background: #f9fafb;
    --button-hover-border: #9ca3af;

    --input-background: #ffffff;
    --input-text: #374151;
    --input-border: #d1d5db;
    --input-border-hover: #9ca3af;
    --input-border-focus: #00d1b2;

    --dropdown-background: #ffffff;
    --dropdown-border: #e5e7eb;
    --dropdown-item-hover: #f9fafb;

    --modal-background: #ffffff;
    --modal-text: #374151;
    --modal-overlay: rgba(0, 0, 0, 0.5);

    --table-background: #ffffff;
    --table-header-background: #f9fafb;
    --table-border: #e5e7eb;

    /* Spacing and sizing */
    --radius: 0.375rem;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    /* Transitions */
    --transition-fast: all 150ms ease;
    --transition-normal: all 300ms ease;
    --transition-slow: all 500ms ease;
    --transition-theme: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Dark mode theme variables */
  .dark {
    --color-primary: #33fff3;
    --color-primary-dark: #00d1b2;
    --color-primary-light: #66fff6;

    --color-link: #38bdf8;
    --color-link-hover: #7dd3fc;
    --color-info: #60a5fa;
    --color-success: #4ade80;
    --color-warning: #fbbf24;
    --color-danger: #f87171;

    --color-background: #111827;
    --color-surface: #1f2937;
    --color-surface-hover: #374151;
    --color-card: #1f2937;
    --color-modal: #1f2937;

    --color-text: #e5e7eb;
    --color-text-strong: #ffffff;
    --color-text-muted: #9ca3af;
    --color-text-light: #6b7280;
    --color-text-inverse: #111827;

    --color-border: #4b5563;
    --color-border-hover: #6b7280;

    --navbar-background: #1f2937;
    --navbar-text: #e5e7eb;
    --navbar-border: #374151;

    --sidebar-background: #1f2937;
    --sidebar-text: #e5e7eb;
    --sidebar-border: #374151;

    --button-background: #374151;
    --button-text: #e5e7eb;
    --button-border: #4b5563;
    --button-hover-background: #4b5563;
    --button-hover-border: #6b7280;

    --input-background: #374151;
    --input-text: #e5e7eb;
    --input-border: #4b5563;
    --input-border-hover: #6b7280;
    --input-border-focus: #33fff3;

    --dropdown-background: #1f2937;
    --dropdown-border: #374151;
    --dropdown-item-hover: #374151;

    --modal-background: #1f2937;
    --modal-text: #e5e7eb;
    --modal-overlay: rgba(0, 0, 0, 0.7);

    --table-background: #1f2937;
    --table-header-background: #374151;
    --table-border: #374151;
  }

  /* Base element styles */
  html {
    @apply scroll-smooth;
  }

  body {
    @apply bg-white dark:bg-gray-900 text-gray-700 dark:text-gray-200 font-sans leading-normal transition-colors duration-300;
    background-color: var(--color-background);
    color: var(--color-text);
  }

  /* Focus styles */
  *:focus {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2 ring-offset-white dark:ring-offset-gray-900;
  }

  /* Selection styles */
  ::selection {
    @apply bg-primary-100 text-primary-900;
  }

  .dark ::selection {
    @apply bg-primary-800 text-primary-100;
  }

  /* Scrollbar styles */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }

  /* Typography improvements */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold text-gray-900 dark:text-white;
  }

  a {
    @apply text-secondary-500 hover:text-secondary-600 dark:text-secondary-400 dark:hover:text-secondary-300 transition-colors;
  }

  /* Form elements base styles */
  input, textarea, select {
    @apply transition-colors duration-200;
  }

  /* Button reset */
  button {
    @apply transition-all duration-200;
  }
}
