<template>
  <div class="preferences-tab">
    <div class="notification is-info is-light" v-if="successMessage">
      <button class="delete" @click="successMessage = ''"></button>
      {{ successMessage }}
    </div>

    <div class="notification is-danger is-light" v-if="errorMessage">
      <button class="delete" @click="errorMessage = ''"></button>
      {{ errorMessage }}
    </div>

    <!-- Theme Settings -->
    <div class="field-group">
      <h3 class="subtitle is-5">
        <span class="icon">
          <i class="fas fa-palette"></i>
        </span>
        Appearance
      </h3>

      <!-- Theme Mode Selection -->
      <div class="field">
        <label class="label" id="theme-mode-label">Theme Mode</label>
        <div class="control">
          <div class="theme-mode-selector" role="radiogroup" aria-labelledby="theme-mode-label">
            <label class="theme-mode-option" :class="{ 'is-active': themeMode === 'light' }">
              <input type="radio" name="theme-mode" value="light" v-model="themeMode" @change="handleModeChange"
                :disabled="isThemeLoading" aria-describedby="theme-mode-help">
              <span class="mode-icon">
                <i class="fas fa-sun"></i>
              </span>
              <span class="mode-text">Light</span>
            </label>

            <label class="theme-mode-option" :class="{ 'is-active': themeMode === 'dark' }">
              <input type="radio" name="theme-mode" value="dark" v-model="themeMode" @change="handleModeChange"
                :disabled="isThemeLoading" aria-describedby="theme-mode-help">
              <span class="mode-icon">
                <i class="fas fa-moon"></i>
              </span>
              <span class="mode-text">Dark</span>
            </label>

            <label class="theme-mode-option" :class="{ 'is-active': themeMode === 'auto' }">
              <input type="radio" name="theme-mode" value="auto" v-model="themeMode" @change="handleModeChange"
                :disabled="isThemeLoading" aria-describedby="theme-mode-help">
              <span class="mode-icon">
                <i class="fas fa-adjust"></i>
              </span>
              <span class="mode-text">Auto</span>
            </label>
          </div>
        </div>
        <p class="help" id="theme-mode-help">Choose how the theme should be determined</p>
      </div>

      <!-- Theme Selection (when not in auto mode) -->
      <div class="field" v-if="themeMode !== 'auto'">
        <label class="label" id="theme-selection-label">
          {{ themeMode === 'light' ? 'Light Theme' : 'Dark Theme' }}
        </label>
        <div class="theme-grid" role="radiogroup" aria-labelledby="theme-selection-label">
          <ThemePreview v-for="theme in filteredThemes" :key="theme.name" :theme="theme"
            :selected="selectedTheme === theme.name" :loading="isThemeLoading && pendingTheme === theme.name"
            @select="handleThemeSelect" :tabindex="0" @keydown="handleThemeKeydown($event, theme.name)" />
        </div>
        <p class="help">Select a specific theme variant</p>
      </div>

      <!-- Auto Mode Info -->
      <div class="field" v-if="themeMode === 'auto'">
        <div class="notification is-info is-light">
          <p>
            <strong>Auto mode</strong> will automatically switch between light and dark themes
            based on your system preference.
          </p>
          <p class="mt-2">
            Current system preference:
            <strong>{{ systemPreference }}</strong>
            <span v-if="currentTheme">
              (using {{ getCurrentThemeDisplayName() }})
            </span>
          </p>
        </div>
      </div>

      <!-- Theme Loading State -->
      <div class="field" v-if="isThemeLoading">
        <div class="notification is-info is-light">
          <div class="is-flex is-align-items-center">
            <span class="icon mr-2">
              <i class="fas fa-spinner fa-spin"></i>
            </span>
            <span>Loading theme...</span>
          </div>
        </div>
      </div>

      <!-- Theme Error State -->
      <div class="field" v-if="themeError">
        <div class="notification is-danger is-light">
          <button class="delete" @click="clearThemeErrorHandler"></button>
          <p><strong>Theme Error:</strong> {{ themeError }}</p>
          <button class="button is-small is-danger is-outlined mt-2" @click="resetToDefaultTheme">
            Reset to Default Theme
          </button>
        </div>
      </div>
    </div>

    <!-- Language Settings -->
    <div class="field-group">
      <h3 class="subtitle is-5">
        <span class="icon">
          <i class="fas fa-globe"></i>
        </span>
        Language & Region
      </h3>

      <div class="field">
        <label class="label">Language</label>
        <div class="control">
          <div class="select is-fullwidth">
            <select v-model="localPreferences.language" @change="updateLanguage" :disabled="isLoading">
              <option value="en">English</option>
              <option value="es">Español</option>
              <option value="fr">Français</option>
              <option value="de">Deutsch</option>
              <option value="it">Italiano</option>
              <option value="pt">Português</option>
              <option value="ja">日本語</option>
              <option value="ko">한국어</option>
              <option value="zh">中文</option>
            </select>
          </div>
        </div>
        <p class="help">Select your preferred language</p>
      </div>

      <div class="field">
        <label class="label">Timezone</label>
        <div class="control">
          <input class="input" type="text" v-model="localPreferences.timezone" @blur="updateTimezone"
            :disabled="isLoading" placeholder="UTC">
        </div>
        <p class="help">Your timezone (e.g., America/New_York, Europe/London)</p>
      </div>
    </div>

    <!-- Editor Settings -->
    <div class="field-group">
      <h3 class="subtitle is-5">
        <span class="icon">
          <i class="fas fa-edit"></i>
        </span>
        Editor
      </h3>

      <div class="field">
        <label class="label">Auto-save Interval</label>
        <div class="control">
          <div class="select is-fullwidth">
            <select v-model="localPreferences.autoSaveInterval" @change="updateAutoSaveInterval" :disabled="isLoading">
              <option :value="5000">5 seconds</option>
              <option :value="10000">10 seconds</option>
              <option :value="30000">30 seconds</option>
              <option :value="60000">1 minute</option>
              <option :value="120000">2 minutes</option>
              <option :value="300000">5 minutes</option>
            </select>
          </div>
        </div>
        <p class="help">How often to automatically save your notes</p>
      </div>
    </div>

    <!-- Notification Settings -->
    <div class="field-group">
      <h3 class="subtitle is-5">
        <span class="icon">
          <i class="fas fa-bell"></i>
        </span>
        Notifications
      </h3>

      <div class="field">
        <div class="control">
          <label class="checkbox">
            <input type="checkbox" v-model="localPreferences.notifications.email" @change="updateNotifications"
              :disabled="isLoading">
            Email notifications
          </label>
        </div>
        <p class="help">Receive notifications via email</p>
      </div>

      <div class="field">
        <div class="control">
          <label class="checkbox">
            <input type="checkbox" v-model="localPreferences.notifications.push" @change="updateNotifications"
              :disabled="isLoading">
            Push notifications
          </label>
        </div>
        <p class="help">Receive browser push notifications</p>
      </div>

      <div class="field">
        <div class="control">
          <label class="checkbox">
            <input type="checkbox" v-model="localPreferences.notifications.mentions" @change="updateNotifications"
              :disabled="isLoading">
            Mention notifications
          </label>
        </div>
        <p class="help">Get notified when someone mentions you</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { useSettingsStore } from '../../stores/settings'
import { useAuthStore } from '../../stores/auth'
import { useTheme } from '../../composables/useTheme'
import ThemePreview from './ThemePreview.vue'
import type { UserPreferences } from '../../services/userService'

const settingsStore = useSettingsStore()
const authStore = useAuthStore()
const {
  currentTheme,
  currentMode,
  availableThemes,
  lightThemes,
  darkThemes,
  systemPreference,
  setTheme,
  clearError: clearThemeError,
  resetToDefault: resetToDefaultTheme
} = useTheme()

// Use the settings store loading state instead of useTheme loading state
const isThemeLoading = computed(() => settingsStore.isThemeLoading)
const themeError = computed(() => settingsStore.themeError)

// Clear theme error method
const clearThemeErrorHandler = () => {
  settingsStore.clearThemeError()
}



const isLoading = ref(false)
const successMessage = ref('')
const errorMessage = ref('')
const pendingTheme = ref<string | null>(null)

// Local theme state
const themeMode = ref<'light' | 'dark' | 'auto'>('auto')
const selectedTheme = ref<string>('default')

const localPreferences = reactive<UserPreferences>({
  theme: 'auto',
  language: 'en',
  timezone: 'UTC',
  autoSaveInterval: 30000,
  notifications: {
    email: true,
    push: true,
    mentions: true
  }
})

// Computed properties for theme filtering
const filteredThemes = computed(() => {
  if (themeMode.value === 'light') {
    return lightThemes.value
  } else if (themeMode.value === 'dark') {
    return darkThemes.value
  }
  return []
})

// Watch for changes in the store and update local preferences
watch(() => settingsStore.preferences, (newPreferences) => {
  Object.assign(localPreferences, newPreferences)
}, { deep: true, immediate: true })

// Watch for theme changes from the composable
const currentModeValue = computed(() => {
  const mode = currentMode.value
  return (typeof mode === 'string' && (mode === 'light' || mode === 'dark' || mode === 'auto')) ? mode : 'auto'
})

watch(currentModeValue, (newMode) => {
  themeMode.value = newMode
}, { immediate: true })

const currentThemeValue = computed(() => {
  const theme = currentTheme.value
  return typeof theme === 'string' ? theme : 'default'
})

watch(currentThemeValue, (newTheme) => {
  selectedTheme.value = newTheme
}, { immediate: true })

const showSuccess = (message: string) => {
  successMessage.value = message
  errorMessage.value = ''
  setTimeout(() => {
    successMessage.value = ''
  }, 3000)
}

const showError = (message: string) => {
  errorMessage.value = message
  successMessage.value = ''
}

// Theme handling methods
const handleModeChange = async () => {
  try {
    // Use the settings store method which handles everything internally
    const result = await settingsStore.updateThemeMode(themeMode.value)

    if (result.success) {
      // Update local preferences
      localPreferences.theme = themeMode.value
      showSuccess('Theme mode updated successfully')
    } else {
      showError(result.error || 'Failed to save theme preference')
    }
  } catch (error) {
    showError('Failed to update theme mode')
    console.error('Theme mode change error:', error)
  } finally {
    // Ensure loading state is cleared
    settingsStore.clearThemeError()
  }
}

const handleThemeSelect = async (themeName: string) => {
  if (selectedTheme.value === themeName || isThemeLoading.value) {
    return
  }

  try {
    pendingTheme.value = themeName
    await setTheme(themeName)

    // Update local preferences and save to backend
    localPreferences.theme = themeName as 'light' | 'dark' | 'auto'
    const result = await settingsStore.updateSpecificTheme(themeName)

    if (result.success) {
      authStore.saveThemePreference(themeName as 'light' | 'dark' | 'auto')
      showSuccess(`Theme changed to ${getThemeDisplayName(themeName)}`)
    } else {
      showError(result.error || 'Failed to save theme preference')
    }
  } catch (error) {
    showError(`Failed to apply theme: ${getThemeDisplayName(themeName)}`)
    console.error('Theme selection error:', error)
  } finally {
    pendingTheme.value = null
  }
}

const handleThemeKeydown = (event: KeyboardEvent, themeName: string) => {
  if (event.key === 'Enter' || event.key === ' ') {
    event.preventDefault()
    handleThemeSelect(themeName)
  }
}

const getThemeDisplayName = (themeName: string): string => {
  const themesArray = Array.isArray(availableThemes.value) ? availableThemes.value : []
  const theme = themesArray.find((t: any) => t && t.name === themeName)
  return theme?.displayName || themeName
}

const getCurrentThemeDisplayName = (): string => {
  return getThemeDisplayName(currentThemeValue.value)
}

const updateTheme = async () => {
  // Legacy method for backward compatibility
  await handleModeChange()
}

const updateLanguage = async () => {
  isLoading.value = true
  try {
    const result = await settingsStore.updateLanguage(localPreferences.language)
    if (result.success) {
      showSuccess('Language updated successfully')
    } else {
      showError(result.error || 'Failed to update language')
    }
  } catch (error) {
    showError('Failed to update language')
  } finally {
    isLoading.value = false
  }
}

const updateTimezone = async () => {
  isLoading.value = true
  try {
    const result = await settingsStore.updateSettings({ timezone: localPreferences.timezone })
    if (result.success) {
      showSuccess('Timezone updated successfully')
    } else {
      showError(result.error || 'Failed to update timezone')
    }
  } catch (error) {
    showError('Failed to update timezone')
  } finally {
    isLoading.value = false
  }
}

const updateAutoSaveInterval = async () => {
  isLoading.value = true
  try {
    const result = await settingsStore.updateAutoSaveInterval(localPreferences.autoSaveInterval)
    if (result.success) {
      showSuccess('Auto-save interval updated successfully')
    } else {
      showError(result.error || 'Failed to update auto-save interval')
    }
  } catch (error) {
    showError('Failed to update auto-save interval')
  } finally {
    isLoading.value = false
  }
}

const updateNotifications = async () => {
  isLoading.value = true
  try {
    const result = await settingsStore.updateNotifications(localPreferences.notifications)
    if (result.success) {
      showSuccess('Notification preferences updated successfully')
    } else {
      showError(result.error || 'Failed to update notification preferences')
    }
  } catch (error) {
    showError('Failed to update notification preferences')
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  if (!settingsStore.preferences.theme) {
    settingsStore.loadSettings()
  }
})
</script>

<style scoped>
.preferences-tab {
  max-width: 600px;
}

.field-group {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e5e5e5;
}

.field-group:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.subtitle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  color: #363636;
}

.field {
  margin-bottom: 1rem;
}

.label {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.help {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: normal;
}

.notification {
  margin-bottom: 1rem;
}

/* Theme Mode Selector */
.theme-mode-selector {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.theme-mode-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  border: 2px solid var(--color-border);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--card-background);
  position: relative;
}

.theme-mode-option:hover {
  border-color: var(--color-primary);
  background: var(--color-surface-hover);
}

.theme-mode-option.is-active {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
  box-shadow: 0 0 0 2px var(--color-primary-alpha);
}

.theme-mode-option input[type="radio"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.mode-icon {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #6b7280;
  transition: color 0.2s ease;
}

.theme-mode-option.is-active .mode-icon {
  color: #3273dc;
}

.mode-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.theme-mode-option.is-active .mode-text {
  color: #3273dc;
}

/* Theme Grid */
.theme-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .theme-mode-selector {
    flex-direction: column;
  }

  .theme-grid {
    grid-template-columns: 1fr;
  }
}

/* Focus styles for accessibility */
.theme-mode-option:focus-within {
  outline: 2px solid #3273dc;
  outline-offset: 2px;
}

/* Dark mode styles */
:global(.dark) .field-group {
  border-bottom-color: #404040;
}

:global(.dark) .subtitle {
  color: #e5e5e5;
}

:global(.dark) .help {
  color: #9ca3af;
}

:global(.dark) .input,
:global(.dark) .select select {
  background: #374151;
  border-color: #4b5563;
  color: #e5e5e5;
}

:global(.dark) .input:focus,
:global(.dark) .select select:focus {
  border-color: #6366f1;
  box-shadow: 0 0 0 0.125em rgba(99, 102, 241, 0.25);
}

:global(.dark) .label {
  color: #e5e5e5;
}

:global(.dark) .theme-mode-option {
  background: #374151;
  border-color: #4b5563;
}

:global(.dark) .theme-mode-option:hover {
  border-color: #6366f1;
  background: #4b5563;
}

:global(.dark) .theme-mode-option.is-active {
  border-color: #6366f1;
  background: #4338ca;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

:global(.dark) .theme-mode-option.is-active .mode-icon,
:global(.dark) .theme-mode-option.is-active .mode-text {
  color: #e0e7ff;
}

:global(.dark) .mode-text {
  color: #d1d5db;
}

:global(.dark) .mode-icon {
  color: #9ca3af;
}
</style>