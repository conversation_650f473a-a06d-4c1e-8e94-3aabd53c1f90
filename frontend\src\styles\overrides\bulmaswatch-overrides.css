/* Bulmaswatch Theme System Overrides */
/* These overrides ensure our theme system works with Bulmaswatch CSS */

/* Input Elements - Use higher specificity instead of !important */
[data-theme] .input,
[data-theme] .textarea,
[data-theme] .select select {
  background-color: var(--input-background);
  border-color: var(--input-border);
  color: var(--input-text);
}

[data-theme] .input:hover,
[data-theme] .textarea:hover,
[data-theme] .select select:hover {
  border-color: var(--input-border-hover);
}

[data-theme] .input:focus,
[data-theme] .textarea:focus,
[data-theme] .select select:focus,
[data-theme] .input.is-focused,
[data-theme] .textarea.is-focused,
[data-theme] .select select.is-focused {
  border-color: var(--input-focus-border);
  box-shadow: 0 0 0 0.125em var(--input-focus-shadow);
}

[data-theme] .input::placeholder,
[data-theme] .textarea::placeholder {
  color: var(--input-placeholder);
}

/* Button Elements - Use higher specificity */
[data-theme] .button {
  background-color: var(--button-background);
  border-color: var(--button-border);
  color: var(--button-text);
}

[data-theme] .button:hover,
[data-theme] .button.is-hovered {
  background-color: var(--button-hover-background);
  border-color: var(--button-hover-border);
  color: var(--button-hover-text);
}

[data-theme] .button:focus,
[data-theme] .button.is-focused {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 0.125em var(--color-primary-alpha);
}

/* Card Elements - Use higher specificity */
[data-theme] .card,
[data-theme] .box {
  background-color: var(--card-background);
  color: var(--card-text);
  border-color: var(--card-border);
}

[data-theme] .card-header {
  background-color: var(--card-header-background);
}

/* Modal Elements - Use higher specificity */
[data-theme] .modal-card,
[data-theme] .modal-content {
  background-color: var(--modal-background);
  color: var(--modal-text);
}

[data-theme] .modal-background {
  background-color: var(--modal-overlay);
}

/* Dropdown Elements - Force theme variables */
.dropdown-menu {
  background-color: var(--dropdown-background) !important;
  border-color: var(--dropdown-border) !important;
}

.dropdown-item {
  color: var(--color-text) !important;
}

.dropdown-item:hover {
  background-color: var(--dropdown-item-hover) !important;
}

/* Navbar Elements - Force theme variables */
.navbar {
  background-color: var(--navbar-background) !important;
  color: var(--navbar-text) !important;
  border-color: var(--navbar-border) !important;
}

.navbar-item {
  color: var(--navbar-text) !important;
}

.navbar-item:hover {
  background-color: var(--navbar-item-hover) !important;
}

/* Sidebar Elements - Force theme variables */
.menu {
  background-color: var(--sidebar-background) !important;
  color: var(--sidebar-text) !important;
}

.menu-item {
  color: var(--sidebar-text) !important;
}

.menu-item:hover {
  background-color: var(--sidebar-item-hover) !important;
}

/* Table Elements - Force theme variables */
.table {
  background-color: var(--table-background) !important;
  color: var(--color-text) !important;
}

.table th {
  background-color: var(--table-header-background) !important;
  color: var(--color-text-strong) !important;
}

.table td {
  border-color: var(--table-border) !important;
}

.table tbody tr:hover {
  background-color: var(--table-row-hover) !important;
}

/* Notification Elements - Force theme variables */
.notification.is-success {
  background-color: var(--notification-success-background) !important;
  color: var(--notification-success-text) !important;
  border-color: var(--notification-success-border) !important;
}

.notification.is-danger {
  background-color: var(--notification-danger-background) !important;
  color: var(--notification-danger-text) !important;
  border-color: var(--notification-danger-border) !important;
}

.notification.is-warning {
  background-color: var(--notification-warning-background) !important;
  color: var(--notification-warning-text) !important;
  border-color: var(--notification-warning-border) !important;
}

.notification.is-info {
  background-color: var(--notification-info-background) !important;
  color: var(--notification-info-text) !important;
  border-color: var(--notification-info-border) !important;
}

/* General Background and Text Overrides */
body {
  background-color: var(--color-background) !important;
  color: var(--color-text) !important;
}

/* Override any hardcoded white backgrounds */
[style*='background: white'],
[style*='background-color: white'],
[style*='background:#fff'],
[style*='background-color:#fff'] {
  background-color: var(--color-background) !important;
}

/* Override any hardcoded black text */
[style*='color: black'],
[style*='color:#000'] {
  color: var(--color-text) !important;
}

/* Ensure proper text colors in dark mode */
[data-theme='darkly'] .has-text-dark {
  color: var(--color-text-light) !important;
}

[data-theme='darkly'] .has-text-black {
  color: var(--color-text) !important;
}

/* Fix any remaining white backgrounds in components */
.search-container,
.note-list-header,
.note-cards-container,
.dashboard-header,
.page-header {
  background-color: var(--color-surface) !important;
}

/* Ensure search inputs specifically use theme colors */
input[placeholder*='Search'],
input[placeholder*='search'] {
  background-color: var(--input-background) !important;
  border-color: var(--input-border) !important;
  color: var(--input-text) !important;
}

input[placeholder*='Search']:focus,
input[placeholder*='search']:focus {
  border-color: var(--input-focus-border) !important;
  box-shadow: 0 0 0 0.125em var(--input-focus-shadow) !important;
}

/* Fix select dropdowns */
.select:not(.is-multiple):not(.is-loading)::after {
  border-color: var(--color-text-muted) !important;
}

.select:hover::after {
  border-color: var(--color-text) !important;
}

/* Fix note titles and action titles */
.note-title,
.action-title {
  color: var(--color-text-strong) !important;
}

.action-description,
.stat-label {
  color: var(--color-text) !important;
}

/* Specific overrides for dashboard components */
.quick-actions-widget .action-title {
  color: var(--color-text-strong) !important;
}

.quick-actions-widget .action-description {
  color: var(--color-text) !important;
}

.stats-grid .stat-label {
  color: var(--color-text) !important;
}

/* Fix note preview text */
.note-preview {
  color: var(--color-text-muted) !important;
}

/* Ensure proper text contrast in dark mode */
[data-theme='darkly'] .note-title,
[data-theme='darkly'] .action-title,
[data-theme='darkly'] h1,
[data-theme='darkly'] h2,
[data-theme='darkly'] h3,
[data-theme='darkly'] h4,
[data-theme='darkly'] h5,
[data-theme='darkly'] h6 {
  color: var(--color-text-strong) !important;
}

[data-theme='darkly'] .action-description,
[data-theme='darkly'] .stat-label,
[data-theme='darkly'] p {
  color: var(--color-text) !important;
}
