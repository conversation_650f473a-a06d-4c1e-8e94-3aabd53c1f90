import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
import { visualizer } from 'rollup-plugin-visualizer'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
    visualizer({
      filename: 'dist/stats.html',
      open: false,
      gzipSize: true,
      brotliSize: true,
    }),
  ],
  css: {
    // Enable source maps for development
    devSourcemap: true,
    // PostCSS is configured in postcss.config.js for Tailwind CSS
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true,
        secure: false,
      },
    },
    host: 'localhost',
    port: 5173
  },
  build: {
    // Enable tree shaking
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Ultra-minimal initial bundle strategy
          
          // Keep main.ts and minimal-app.ts in main bundle (ultra-small)
          if (id.includes('src/main.ts') || id.includes('src/minimal-app.ts')) {
            return undefined // Keep in main bundle
          }
          
          // Everything else goes to lazy chunks
          if (id.includes('src/main-full.ts')) {
            return 'app-full'
          }
          
          // Theme system - split themes into separate chunks for lazy loading
          if (id.includes('src/styles/themes/bulmaswatch/')) {
            const themeName = id.match(/bulmaswatch\/([^\/]+)\.css$/)?.[1]
            if (themeName && themeName !== 'default') {
              return `theme-${themeName}`
            }
            return 'theme-default' // Default theme in separate chunk
          }
          
          // CSS files - split by category for better caching
          if (id.includes('src/styles/')) {
            if (id.includes('base/')) {
              return 'styles-base'
            }
            if (id.includes('components/')) {
              return 'styles-components'
            }
            if (id.includes('utilities/')) {
              return 'styles-utilities'
            }
            if (id.includes('vendor/')) {
              return 'styles-vendor'
            }
            if (id.includes('themes/themes.css')) {
              return 'styles-theme-system'
            }
            return 'styles-main'
          }
          
          // Vue core - ultra-minimal initial bundle
          if (id.includes('vue/dist/vue.runtime.esm-bundler.js')) {
            return 'vue-runtime-core'
          }
          if (id.includes('@vue/runtime-core') || id.includes('@vue/reactivity')) {
            return 'vue-reactivity'
          }
          if (id.includes('vue-router')) {
            return 'vue-router-lazy'
          }
          if (id.includes('pinia')) {
            return 'pinia-lazy'
          }
          if (id.includes('vue') && !id.includes('vue-router') && !id.includes('@tiptap/vue')) {
            return 'vue-misc'
          }
          
          // FontAwesome - lazy load only what's needed
          if (id.includes('@fortawesome')) {
            if (id.includes('brands')) {
              return 'ui-icons-brands'
            }
            if (id.includes('regular')) {
              return 'ui-icons-regular'
            }
            return 'ui-icons-solid'
          }
          
          // Editor - ultra-granular splitting
          if (id.includes('@tiptap/vue-3')) {
            return 'editor-vue'
          }
          if (id.includes('@tiptap/starter-kit')) {
            return 'editor-starter'
          }
          if (id.includes('@tiptap/extension-table')) {
            return 'editor-table'
          }
          if (id.includes('@tiptap/extension-image')) {
            return 'editor-image'
          }
          if (id.includes('@tiptap/extension-link')) {
            return 'editor-link'
          }
          if (id.includes('@tiptap/extension-list')) {
            return 'editor-lists'
          }
          if (id.includes('@tiptap/extension-color') || id.includes('@tiptap/extension-highlight')) {
            return 'editor-formatting'
          }
          if (id.includes('@tiptap')) {
            return 'editor-misc'
          }
          
          // Highlight.js - minimal core only
          if (id.includes('highlight.js/lib/core')) {
            return 'utils-highlight-core'
          }
          if (id.includes('highlight.js/lib/languages')) {
            return 'utils-highlight-langs'
          }
          if (id.includes('highlight.js')) {
            return 'utils-highlight-misc'
          }
          
          // Charts - completely exclude from initial bundle (lazy loaded)
          if (id.includes('chart.js')) {
            return 'charts-lazy'
          }
          if (id.includes('chartjs-adapter')) {
            return 'charts-adapters-lazy'
          }
          
          // Utilities - keep small
          if (id.includes('lodash-es')) {
            // Split lodash by function groups
            if (id.includes('array') || id.includes('collection')) {
              return 'utils-lodash-collections'
            }
            if (id.includes('object') || id.includes('lang')) {
              return 'utils-lodash-objects'
            }
            return 'utils-lodash-misc'
          }
          if (id.includes('date-fns')) {
            return 'utils-date'
          }
          if (id.includes('marked')) {
            return 'utils-marked'
          }
          
          // Google auth
          if (id.includes('google-one-tap')) {
            return 'auth-google'
          }
          
          // Vendor chunk for remaining node_modules
          if (id.includes('node_modules')) {
            return 'vendor'
          }
          
          // Application code - no default chunk to force explicit chunking
          return undefined
        },
        // Optimize CSS chunk naming for better caching
        assetFileNames: (assetInfo) => {
          if (assetInfo.name?.endsWith('.css')) {
            // Theme CSS files get special naming for cache busting
            if (assetInfo.name.includes('theme-')) {
              return 'assets/themes/[name]-[hash][extname]'
            }
            // Other CSS files
            return 'assets/css/[name]-[hash][extname]'
          }
          return 'assets/[name]-[hash][extname]'
        }
      }
    },
    // Reduce chunk size warning limit to enforce smaller bundles
    chunkSizeWarningLimit: 300,
    // Performance budgets for CSS and JS
    reportCompressedSize: true,
    // Enhanced minification settings
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.log in production
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug'],
        passes: 2 // Multiple passes for better compression
      },
      mangle: {
        safari10: true // Better Safari compatibility
      }
    },
    // Disable source maps for smaller bundles
    sourcemap: false,
    // CSS code splitting
    cssCodeSplit: true,
    // Optimize CSS
    cssMinify: true,
  },
  // Performance optimizations
  optimizeDeps: {
    include: [
      'vue',
      'vue-router',
      'pinia',
      '@tiptap/vue-3',
      '@tiptap/starter-kit',
      'marked',
      'highlight.js/lib/core',
      'chart.js',
      'lodash-es',
      'date-fns'
    ],
    exclude: [
      // Exclude development tools from production bundle
      'vite-plugin-vue-devtools',
      '@vue/devtools-api',
      // Exclude heavy dependencies from initial bundle
      'chart.js',
      'chartjs-adapter-date-fns',
      '@fortawesome/fontawesome-free',
      // Exclude editor components from initial load
      '@tiptap/extension-table',
      '@tiptap/extension-image'
    ]
  },
  define: {
    // Aggressive Vue production optimizations
    __VUE_PROD_DEVTOOLS__: false,
    __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false,
    __VUE_OPTIONS_API__: false, // Disable Options API to reduce bundle size
    __VUE_PROD_COMPAT__: false, // Disable Vue 2 compatibility
    'process.env.NODE_ENV': '"production"'
  },
})
