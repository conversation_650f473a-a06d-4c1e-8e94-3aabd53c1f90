/* Button Component Styles */

.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2) var(--spacing-4);
  border: 1px solid var(--button-border);
  border-radius: var(--radius);
  background: var(--button-background);
  color: var(--button-text);
  text-decoration: none;
  cursor: pointer;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-none);
  transition: var(--transition-fast);
  position: relative;
  vertical-align: top;
  white-space: nowrap;
  user-select: none;
}

.button:hover {
  background: var(--button-hover-background);
  border-color: var(--button-hover-border);
  transform: translateY(-1px);
}

.button:active {
  transform: translateY(0);
}

.button:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* <PERSON><PERSON> Variants */
.button.is-primary {
  background: var(--color-primary) !important;
  color: var(--color-text) !important;
  border-color: var(--color-primary) !important;
}

.button.is-primary:hover {
  background: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
}

.button.is-success {
  background: var(--color-success);
  color: white;
  border-color: var(--color-success);
}

.button.is-success:hover {
  background: var(--color-success-dark);
  border-color: var(--color-success-dark);
}

.button.is-danger {
  background: var(--color-danger);
  color: white;
  border-color: var(--color-danger);
}

.button.is-danger:hover {
  background: var(--color-danger-dark);
  border-color: var(--color-danger-dark);
}

.button.is-warning {
  background: var(--color-warning);
  color: rgba(0, 0, 0, 0.7);
  border-color: var(--color-warning);
}

.button.is-warning:hover {
  background: var(--color-warning-dark);
  border-color: var(--color-warning-dark);
}

.button.is-info {
  background: var(--color-info);
  color: white;
  border-color: var(--color-info);
}

.button.is-info:hover {
  background: var(--color-info-dark);
  border-color: var(--color-info-dark);
}

.button.is-link {
  background: var(--color-link);
  color: white;
  border-color: var(--color-link);
}

.button.is-link:hover {
  background: var(--color-link-dark);
  border-color: var(--color-link-dark);
}

/* Button Sizes */
.button.is-small {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.button.is-medium {
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-lg);
}

.button.is-large {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--font-size-xl);
}

/* Button Modifiers */
.button.is-fullwidth {
  display: flex;
  width: 100%;
}

.button.is-loading {
  opacity: 0.7;
  pointer-events: none;
  position: relative;
}

.button.is-loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.button.is-ghost {
  background: transparent;
  border-color: transparent;
  color: var(--color-text);
}

.button.is-ghost:hover {
  background: var(--color-surface-hover);
  border-color: var(--color-border);
}

.button.is-outlined {
  background: transparent;
  color: var(--color-primary);
}

.button.is-outlined.is-primary {
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.button.is-outlined.is-primary:hover {
  background: var(--color-primary);
  color: white;
}

.button.is-outlined.is-success {
  color: var(--color-success);
  border-color: var(--color-success);
}

.button.is-outlined.is-success:hover {
  background: var(--color-success);
  color: white;
}

.button.is-outlined.is-danger {
  color: var(--color-danger);
  border-color: var(--color-danger);
}

.button.is-outlined.is-danger:hover {
  background: var(--color-danger);
  color: white;
}

.button.is-outlined.is-warning {
  color: var(--color-warning-dark);
  border-color: var(--color-warning);
}

.button.is-outlined.is-warning:hover {
  background: var(--color-warning);
  color: rgba(0, 0, 0, 0.7);
}

.button.is-outlined.is-info {
  color: var(--color-info);
  border-color: var(--color-info);
}

.button.is-outlined.is-info:hover {
  background: var(--color-info);
  color: white;
}

/* Button Groups */
.buttons {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
  align-items: center;
}

.buttons.has-addons {
  gap: 0;
}

.buttons.has-addons .button {
  border-radius: 0;
  margin-right: -1px;
}

.buttons.has-addons .button:first-child {
  border-top-left-radius: var(--radius);
  border-bottom-left-radius: var(--radius);
}

.buttons.has-addons .button:last-child {
  border-top-right-radius: var(--radius);
  border-bottom-right-radius: var(--radius);
  margin-right: 0;
}

.buttons.has-addons .button:hover,
.buttons.has-addons .button:focus {
  z-index: 2;
}

.buttons.has-addons .button:active {
  z-index: 3;
}

.buttons.is-centered {
  justify-content: center;
}

.buttons.is-right {
  justify-content: flex-end;
}

/* Animation for loading state */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Enhanced Button Styling for Theme Integration */

/* Ghost buttons - used throughout the app for secondary actions */
.button.is-ghost {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  color: var(--color-text-muted);
  border-radius: var(--radius);
  transition: var(--transition-fast);
}

.button.is-ghost:hover {
  background: var(--color-surface-hover);
  border-color: var(--color-primary);
  color: var(--color-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.button.is-ghost:active {
  transform: translateY(0);
  box-shadow: none;
}

/* Small ghost buttons - for action buttons */
.button.is-ghost.is-small {
  padding: 0.25rem 0.375rem;
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

/* Dropdown trigger buttons */
.dropdown-trigger .button {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  color: var(--color-text-muted);
  border-radius: var(--radius);
  transition: var(--transition-fast);
}

.dropdown-trigger .button:hover,
.dropdown.is-active .dropdown-trigger .button {
  background: var(--color-surface-hover);
  border-color: var(--color-primary);
  color: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

/* Note action buttons */
.note-actions .button,
.note-item-actions .button {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  color: var(--color-text-muted);
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
  opacity: 0.8;
}

.note-actions .button:hover,
.note-item-actions .button:hover {
  background: var(--color-surface-hover);
  border-color: var(--color-primary);
  color: var(--color-primary);
  opacity: 1;
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* Ellipsis buttons specifically */
.button .fa-ellipsis-v,
.button .fa-ellipsis-h {
  opacity: 0.7;
  transition: opacity var(--transition-fast);
}

.button:hover .fa-ellipsis-v,
.button:hover .fa-ellipsis-h {
  opacity: 1;
}

/* User dropdown button */
.user-actions .button {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  color: var(--color-text-muted);
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
}

.user-actions .button:hover {
  background: var(--color-surface-hover);
  border-color: var(--color-primary);
  color: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

/* Improved focus states for accessibility */
.button.is-ghost:focus,
.dropdown-trigger .button:focus,
.note-actions .button:focus,
.note-item-actions .button:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  border-color: var(--color-primary);
}

/* Special styling for create/add buttons */
.sidebar-section-header .button[title*='Create'],
.sidebar-section-header .button[title*='Add'],
.collapsed-create-button .button[title*='Create'] {
  background: var(--color-primary-alpha);
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.sidebar-section-header .button[title*='Create']:hover,
.sidebar-section-header .button[title*='Add']:hover,
.collapsed-create-button .button[title*='Create']:hover {
  background: var(--color-primary);
  color: var(--color-text-inverse);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Settings/cog buttons */
.sidebar-section-header .button[title*='settings'],
.sidebar-section-header .button[title*='Settings'],
.collapsed-create-button .button[title*='Settings'] {
  background: var(--color-surface);
  border-color: var(--color-border);
  color: var(--color-text-muted);
}

.sidebar-section-header .button[title*='settings']:hover,
.sidebar-section-header .button[title*='Settings']:hover,
.collapsed-create-button .button[title*='Settings']:hover {
  background: var(--color-info-alpha, rgba(50, 152, 220, 0.1));
  border-color: var(--color-info);
  color: var(--color-info);
}

/* Responsive button adjustments */
@media screen and (max-width: 768px) {
  .button.is-responsive {
    font-size: var(--font-size-sm);
    padding: var(--spacing-2) var(--spacing-3);
  }

  .buttons {
    gap: var(--spacing-1);
  }

  .button.is-ghost.is-small {
    padding: 0.375rem 0.5rem;
  }

  .note-actions .button,
  .note-item-actions .button {
    opacity: 1; /* Always visible on mobile */
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .button.is-ghost {
    border-width: 2px;
  }

  .button.is-ghost:hover {
    border-width: 2px;
  }
}
