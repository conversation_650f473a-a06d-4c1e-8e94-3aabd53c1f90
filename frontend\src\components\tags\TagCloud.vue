<template>
  <div class="tag-cloud">
    <div class="tag-cloud-header" v-if="showHeader">
      <div class="level">
        <div class="level-left">
          <div class="level-item">
            <h3 class="title is-5">
              <span class="icon">
                <i class="fas fa-cloud"></i>
              </span>
              <span>{{ title }}</span>
            </h3>
          </div>
        </div>
        <div class="level-right">
          <div class="level-item">
            <div class="buttons are-small">
              <button
                class="button is-light"
                :class="{ 'is-primary': viewMode === 'cloud' }"
                @click="viewMode = 'cloud'"
                title="Cloud View"
              >
                <span class="icon">
                  <i class="fas fa-cloud"></i>
                </span>
              </button>
              <button
                class="button is-light"
                :class="{ 'is-primary': viewMode === 'list' }"
                @click="viewMode = 'list'"
                title="List View"
              >
                <span class="icon">
                  <i class="fas fa-list"></i>
                </span>
              </button>
              <button
                class="button is-light"
                :class="{ 'is-primary': viewMode === 'grid' }"
                @click="viewMode = 'grid'"
                title="Grid View"
              >
                <span class="icon">
                  <i class="fas fa-th"></i>
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Search and Filter -->
    <div v-if="showSearch" class="tag-cloud-search mb-4">
      <div class="field has-addons">
        <div class="control has-icons-left is-expanded">
          <input
            v-model="searchQuery"
            type="text"
            class="input is-small"
            placeholder="Search tags..."
          />
          <span class="icon is-small is-left">
            <i class="fas fa-search"></i>
          </span>
        </div>
        <div class="control">
          <div class="select is-small">
            <select v-model="sortBy">
              <option value="usage">Usage</option>
              <option value="name">Name</option>
              <option value="recent">Recent</option>
            </select>
          </div>
        </div>
        <div class="control" v-if="showFilters">
          <button
            class="button is-small"
            :class="{ 'is-primary': showUnusedTags }"
            @click="showUnusedTags = !showUnusedTags"
            title="Show unused tags"
          >
            <span class="icon">
              <i class="fas fa-eye-slash"></i>
            </span>
          </button>
        </div>
      </div>
    </div>

    <!-- Tag Cloud Content -->
    <div class="tag-cloud-content" :class="`view-${viewMode}`">
      <!-- Cloud View -->
      <div v-if="viewMode === 'cloud'" class="tag-cloud-view">
        <div
          v-for="tagData in processedTags"
          :key="tagData.tag.id"
          class="tag-cloud-item"
          :class="getTagCloudClasses(tagData)"
          :style="{ color: tagData.color }"
          @click="selectTag(tagData.tag)"
          @mouseenter="highlightTag(tagData.tag)"
          @mouseleave="unhighlightTag"
        >
          <span class="tag-cloud-name">{{ tagData.tag.name }}</span>
          <span v-if="showCounts" class="tag-cloud-count">({{ tagData.usage }})</span>
        </div>
      </div>

      <!-- List View -->
      <div v-if="viewMode === 'list'" class="tag-list-view">
        <div
          v-for="tagData in processedTags"
          :key="tagData.tag.id"
          class="tag-list-item"
          @click="selectTag(tagData.tag)"
        >
          <div class="tag-list-content">
            <div class="tag-list-main">
              <span class="tag-list-name">{{ tagData.tag.name }}</span>
              <div class="tag-list-usage">
                <div class="tag-list-bar">
                                  <div
                  class="tag-list-fill usage-bar-fill"
                  :style="{ width: getUsagePercentage(tagData.usage) + '%' }"
                ></div>
                </div>
                <span class="tag-list-count">{{ tagData.usage }}</span>
              </div>
            </div>
            <div class="tag-list-actions">
              <button
                class="button is-small is-light"
                @click.stop="viewTagNotes(tagData.tag)"
                title="View notes"
              >
                <span class="icon">
                  <i class="fas fa-eye"></i>
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Grid View -->
      <div v-if="viewMode === 'grid'" class="tag-grid-view">
        <div
          v-for="tagData in processedTags"
          :key="tagData.tag.id"
          class="tag-grid-item"
          @click="selectTag(tagData.tag)"
        >
          <div class="tag-grid-content">
            <div class="tag-grid-header">
              <span class="tag-grid-name">{{ tagData.tag.name }}</span>
            </div>
            <div class="tag-grid-body">
              <div class="tag-grid-usage">
                <span class="tag-grid-count">{{ tagData.usage }}</span>
                <span class="tag-grid-label">notes</span>
              </div>
            </div>
            <div class="tag-grid-footer">
              <div class="tag-grid-bar">
                <div
                  class="tag-grid-fill usage-bar-fill"
                  :style="{ width: getUsagePercentage(tagData.usage) + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="processedTags.length === 0" class="tag-cloud-empty">
        <div class="has-text-centered py-4">
          <span class="icon is-large has-text-grey-light">
            <i class="fas fa-tags fa-2x"></i>
          </span>
          <p class="title is-5 has-text-grey">
            {{ searchQuery ? 'No matching tags' : 'No tags available' }}
          </p>
          <p class="subtitle is-6 has-text-grey">
            {{ searchQuery ? 'Try adjusting your search' : 'Start creating notes with tags' }}
          </p>
        </div>
      </div>
    </div>

    <!-- Tag Details Tooltip -->
    <div
      v-if="highlightedTag && showTooltip"
      class="tag-tooltip"
    >
      <div class="tag-tooltip-content">
        <div class="tag-tooltip-header">
          <strong>{{ highlightedTag.name }}</strong>
        </div>
        <div class="tag-tooltip-body">
          <p>{{ getTagUsage(highlightedTag.id) }} notes</p>
          <p class="is-size-7 has-text-grey">
            Created {{ formatDate(highlightedTag.createdAt) }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useNotesStore } from '../../stores/notes'
import type { Tag } from '../../services/noteService'

interface TagData {
  tag: Tag
  usage: number
  size: number
  color: string
}

interface Props {
  title?: string
  showHeader?: boolean
  showSearch?: boolean
  showFilters?: boolean
  showCounts?: boolean
  showTooltip?: boolean
  maxTags?: number
  minFontSize?: number
  maxFontSize?: number
  colorScheme?: 'blue' | 'green' | 'purple' | 'rainbow'
  defaultViewMode?: 'cloud' | 'list' | 'grid'
}

interface Emits {
  (e: 'tag-selected', tag: Tag): void
  (e: 'tag-highlighted', tag: Tag | null): void
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Tag Cloud',
  showHeader: true,
  showSearch: true,
  showFilters: true,
  showCounts: true,
  showTooltip: true,
  maxTags: 50,
  minFontSize: 12,
  maxFontSize: 24,
  colorScheme: 'blue',
  defaultViewMode: 'cloud'
})

const emit = defineEmits<Emits>()

const router = useRouter()
const notesStore = useNotesStore()

// Local state
const searchQuery = ref('')
const sortBy = ref<'usage' | 'name' | 'recent'>('usage')
const showUnusedTags = ref(false)
const viewMode = ref(props.defaultViewMode)
const highlightedTag = ref<Tag | null>(null)


// Computed
const tags = computed(() => notesStore.tags)
const tagUsageCount = computed(() => notesStore.tagUsageCount)

const filteredTags = computed(() => {
  let filtered = tags.value

  // Apply search filter
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(tag =>
      tag.name.toLowerCase().includes(query)
    )
  }

  // Apply unused filter
  if (!showUnusedTags.value) {
    filtered = filtered.filter(tag => getTagUsage(tag.id) > 0)
  }

  return filtered
})

const sortedTags = computed(() => {
  const sorted = [...filteredTags.value]

  sorted.sort((a, b) => {
    switch (sortBy.value) {
      case 'usage':
        return getTagUsage(b.id) - getTagUsage(a.id)
      case 'name':
        return a.name.localeCompare(b.name)
      case 'recent':
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      default:
        return 0
    }
  })

  return sorted.slice(0, props.maxTags)
})

const processedTags = computed(() => {
  const maxUsage = Math.max(...sortedTags.value.map(tag => getTagUsage(tag.id)), 1)
  const minUsage = Math.min(...sortedTags.value.map(tag => getTagUsage(tag.id)), 0)
  const usageRange = maxUsage - minUsage || 1

  return sortedTags.value.map((tag, index) => {
    const usage = getTagUsage(tag.id)
    const normalizedUsage = (usage - minUsage) / usageRange
    const size = props.minFontSize + (normalizedUsage * (props.maxFontSize - props.minFontSize))
    const color = getTagColor(normalizedUsage, index)

    return {
      tag,
      usage,
      size,
      color
    }
  })
})

const maxUsage = computed(() => {
  return Math.max(...processedTags.value.map(t => t.usage), 1)
})

// Methods
const getTagUsage = (tagId: string): number => {
  const tag = tags.value.find(t => t.id === tagId)
  return tag ? (tagUsageCount.value.get(tag.name) || 0) : 0
}

const getUsagePercentage = (usage: number): number => {
  return maxUsage.value > 0 ? (usage / maxUsage.value) * 100 : 0
}

const getTagColor = (normalizedUsage: number, index: number): string => {
  // Get CSS custom property values for theme-aware colors
  const rootStyles = getComputedStyle(document.documentElement)
  const primaryColor = rootStyles.getPropertyValue('--color-primary').trim()
  const successColor = rootStyles.getPropertyValue('--color-success').trim()
  const infoColor = rootStyles.getPropertyValue('--color-info').trim()
  const warningColor = rootStyles.getPropertyValue('--color-warning').trim()

  switch (props.colorScheme) {
    case 'blue':
      // Use theme's info color as base, adjust opacity based on usage
      const blueOpacity = 0.3 + (normalizedUsage * 0.7)
      return `color-mix(in srgb, ${infoColor} ${blueOpacity * 100}%, transparent)`

    case 'green':
      // Use theme's success color as base
      const greenOpacity = 0.3 + (normalizedUsage * 0.7)
      return `color-mix(in srgb, ${successColor} ${greenOpacity * 100}%, transparent)`

    case 'purple':
      // Use theme's primary color as base
      const purpleOpacity = 0.3 + (normalizedUsage * 0.7)
      return `color-mix(in srgb, ${primaryColor} ${purpleOpacity * 100}%, transparent)`

    case 'rainbow':
      // For rainbow, still use HSL but with theme-aware lightness
      const hue = (index * 137.508) % 360 // Golden angle approximation
      const saturation = 60 + normalizedUsage * 30
      const lightness = 70 - normalizedUsage * 20
      return `hsl(${hue}, ${saturation}%, ${lightness}%)`

    default:
      // Use theme's primary color
      return primaryColor || '#3273dc'
  }
}

const getTagCloudClasses = (tagData: TagData): string[] => {
  const classes = ['tag-cloud-tag']
  
  // Add size class
  if (tagData.size <= 14) classes.push('tag-cloud-size-sm')
  else if (tagData.size <= 16) classes.push('tag-cloud-size-md')
  else if (tagData.size <= 18) classes.push('tag-cloud-size-lg')
  else if (tagData.size <= 20) classes.push('tag-cloud-size-xl')
  else if (tagData.size <= 22) classes.push('tag-cloud-size-2xl')
  else classes.push('tag-cloud-size-3xl')
  
  // Add weight class
  const weight = Math.min(400 + tagData.usage * 100, 700)
  if (weight <= 500) classes.push('tag-cloud-weight-light')
  else if (weight <= 600) classes.push('tag-cloud-weight-normal')
  else if (weight <= 650) classes.push('tag-cloud-weight-medium')
  else classes.push('tag-cloud-weight-bold')
  
  if (tagData.usage === 0) {
    classes.push('is-unused')
  } else if (tagData.usage >= maxUsage.value * 0.8) {
    classes.push('is-popular')
  } else if (tagData.usage >= maxUsage.value * 0.5) {
    classes.push('is-common')
  } else {
    classes.push('is-rare')
  }

  return classes
}



const selectTag = (tag: Tag) => {
  emit('tag-selected', tag)
}

const viewTagNotes = (tag: Tag) => {
  router.push({
    name: 'Search',
    query: { tags: tag.name }
  })
}

const highlightTag = (tag: Tag) => {
  highlightedTag.value = tag
  emit('tag-highlighted', tag)
}

const unhighlightTag = () => {
  highlightedTag.value = null
  emit('tag-highlighted', null)
}

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffDays === 0) {
    return 'today'
  } else if (diffDays === 1) {
    return 'yesterday'
  } else if (diffDays < 30) {
    return `${diffDays} days ago`
  } else {
    return date.toLocaleDateString()
  }
}

// Watch for search changes
watch(searchQuery, () => {
  // Reset view to show filtered results
})
</script>

<style scoped>
.tag-cloud {
  position: relative;
}

/* Cloud View */
.tag-cloud-view {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
  align-items: center;
  padding: 1rem;
  min-height: 200px;
}

.tag-cloud-item {
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  user-select: none;
  line-height: 1.2;
}

.tag-cloud-item:hover {
  transform: scale(1.1);
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tag-cloud-item.is-unused {
  opacity: 0.5;
}

.tag-cloud-item.is-popular {
  font-weight: bold;
}

.tag-cloud-count {
  font-size: 0.8em;
  opacity: 0.7;
  margin-left: 0.25rem;
}

/* Utility classes for tag cloud styles */
.tag-cloud-size-xs { font-size: 12px; }
.tag-cloud-size-sm { font-size: 14px; }
.tag-cloud-size-md { font-size: 16px; }
.tag-cloud-size-lg { font-size: 18px; }
.tag-cloud-size-xl { font-size: 20px; }
.tag-cloud-size-2xl { font-size: 22px; }
.tag-cloud-size-3xl { font-size: 24px; }

.tag-cloud-weight-light { font-weight: 400; }
.tag-cloud-weight-normal { font-weight: 500; }
.tag-cloud-weight-medium { font-weight: 600; }
.tag-cloud-weight-bold { font-weight: 700; }

/* Usage bar utility classes */
.usage-bar-fill {
  height: 100%;
  background: var(--color-primary);
  transition: width 0.3s ease;
}

/* Tooltip positioning */
.tag-tooltip {
  position: absolute;
  z-index: 1000;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 6px;
  padding: 0.75rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-width: 250px;
  pointer-events: none;
}

/* List View */
.tag-list-view {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.tag-list-item {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tag-list-item:hover {
  border-color: #3273dc;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tag-list-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tag-list-main {
  flex: 1;
}

.tag-list-name {
  font-weight: 600;
  display: block;
  margin-bottom: 0.25rem;
}

.tag-list-usage {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tag-list-bar {
  flex: 1;
  height: 4px;
  background-color: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.tag-list-fill {
  height: 100%;
  background: linear-gradient(90deg, #3273dc, #23d160);
  transition: width 0.3s ease;
}

.tag-list-count {
  font-size: 0.9rem;
  color: #666;
  min-width: 2rem;
  text-align: right;
}

/* Grid View */
.tag-grid-view {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
}

.tag-grid-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tag-grid-item:hover {
  border-color: #3273dc;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.tag-grid-content {
  padding: 1rem;
  text-align: center;
}

.tag-grid-header {
  margin-bottom: 0.5rem;
}

.tag-grid-name {
  font-weight: 600;
  font-size: 1rem;
}

.tag-grid-body {
  margin-bottom: 0.5rem;
}

.tag-grid-count {
  font-size: 1.5rem;
  font-weight: bold;
  color: #3273dc;
}

.tag-grid-label {
  font-size: 0.8rem;
  color: #666;
  display: block;
}

.tag-grid-footer {
  margin-top: 0.5rem;
}

.tag-grid-bar {
  height: 3px;
  background-color: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.tag-grid-fill {
  height: 100%;
  background: linear-gradient(90deg, #3273dc, #23d160);
  transition: width 0.3s ease;
}

/* Tooltip */
.tag-tooltip {
  position: absolute;
  z-index: 1000;
  pointer-events: none;
}

.tag-tooltip-content {
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  max-width: 200px;
}

.tag-tooltip-header {
  margin-bottom: 0.25rem;
}

/* Empty state */
.tag-cloud-empty {
  text-align: center;
  padding: 2rem;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Responsive design */
@media (max-width: 768px) {
  .tag-cloud-view {
    padding: 0.5rem;
  }
  
  .tag-grid-view {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0.5rem;
  }
  
  .tag-list-content {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
  
  .tag-list-usage {
    justify-content: space-between;
  }
}

/* Animation */
.tag-cloud-item,
.tag-list-item,
.tag-grid-item {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}
</style>