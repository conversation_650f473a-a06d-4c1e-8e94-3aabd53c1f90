/* Tailwind Components Layer */
@tailwind components;

@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md bg-white text-gray-700 font-medium text-sm leading-none transition-all duration-200 cursor-pointer select-none whitespace-nowrap;
    background-color: var(--button-background);
    color: var(--button-text);
    border-color: var(--button-border);
  }

  .btn:hover {
    @apply transform -translate-y-px;
    background-color: var(--button-hover-background);
    border-color: var(--button-hover-border);
  }

  .btn:active {
    @apply transform translate-y-0;
  }

  .btn:disabled {
    @apply opacity-60 cursor-not-allowed transform-none;
  }

  .btn-primary {
    @apply bg-primary-500 text-white border-primary-500 hover:bg-primary-600 hover:border-primary-600;
  }

  .btn-secondary {
    @apply bg-secondary-500 text-white border-secondary-500 hover:bg-secondary-600 hover:border-secondary-600;
  }

  .btn-success {
    @apply bg-success-500 text-white border-success-500 hover:bg-success-600 hover:border-success-600;
  }

  .btn-warning {
    @apply bg-warning-500 text-gray-900 border-warning-500 hover:bg-warning-600 hover:border-warning-600;
  }

  .btn-danger {
    @apply bg-danger-500 text-white border-danger-500 hover:bg-danger-600 hover:border-danger-600;
  }

  .btn-outline {
    @apply bg-transparent hover:bg-gray-50 dark:hover:bg-gray-800;
  }

  .btn-outline-primary {
    @apply bg-transparent text-primary-500 border-primary-500 hover:bg-primary-500 hover:text-white;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  /* Form Components */
  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-gray-700 placeholder-gray-400 transition-colors duration-200;
    background-color: var(--input-background);
    color: var(--input-text);
    border-color: var(--input-border);
  }

  .form-input:hover {
    border-color: var(--input-border-hover);
  }

  .form-input:focus {
    @apply ring-2 ring-primary-500 ring-offset-2;
    border-color: var(--input-border-focus);
  }

  .form-input.is-danger {
    @apply border-danger-500 ring-danger-500;
  }

  .form-textarea {
    @apply form-input resize-y min-h-[100px];
  }

  .form-select {
    @apply form-input pr-10 bg-no-repeat bg-right;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-size: 1.5em 1.5em;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1;
  }

  .form-help {
    @apply text-xs text-gray-500 dark:text-gray-400 mt-1;
  }

  .form-help.is-danger {
    @apply text-danger-500;
  }

  .form-field {
    @apply mb-4;
  }

  .form-control {
    @apply relative;
  }

  .form-control .form-icon {
    @apply absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none;
  }

  .form-control .form-icon-right {
    @apply absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 cursor-pointer;
  }

  .form-control.has-icon-left .form-input {
    @apply pl-10;
  }

  .form-control.has-icon-right .form-input {
    @apply pr-10;
  }

  /* Card Components */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden;
    background-color: var(--color-card);
    border-color: var(--color-border);
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700;
    border-color: var(--color-border);
  }

  .card-title {
    @apply text-lg font-semibold text-gray-900 dark:text-white;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700;
    border-color: var(--color-border);
  }

  /* Modal Components */
  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 dark:bg-opacity-70 flex items-center justify-center p-4 z-50;
    background-color: var(--modal-overlay);
  }

  .modal-content {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto;
    background-color: var(--modal-background);
    color: var(--modal-text);
  }

  .modal-header {
    @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between;
  }

  .modal-title {
    @apply text-lg font-semibold text-gray-900 dark:text-white;
  }

  .modal-close {
    @apply text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors;
  }

  .modal-body {
    @apply px-6 py-4;
  }

  .modal-footer {
    @apply px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3;
  }

  /* Navigation Components */
  .navbar {
    @apply bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3;
    background-color: var(--navbar-background);
    color: var(--navbar-text);
    border-color: var(--navbar-border);
  }

  .navbar-brand {
    @apply font-bold text-xl text-gray-900 dark:text-white;
  }

  .navbar-menu {
    @apply flex items-center space-x-4 ml-auto;
  }

  .navbar-item {
    @apply px-3 py-2 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors;
  }

  .sidebar {
    @apply bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 w-64 h-full overflow-y-auto;
    background-color: var(--sidebar-background);
    color: var(--sidebar-text);
    border-color: var(--sidebar-border);
  }

  .sidebar-item {
    @apply block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors;
  }

  .sidebar-item.is-active {
    @apply bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 border-r-2 border-primary-500;
  }

  /* Dropdown Components */
  .dropdown {
    @apply relative inline-block;
  }

  .dropdown-menu {
    @apply absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-50;
    background-color: var(--dropdown-background);
    border-color: var(--dropdown-border);
  }

  .dropdown-item {
    @apply block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors;
  }

  .dropdown-item:hover {
    background-color: var(--dropdown-item-hover);
  }

  .dropdown-divider {
    @apply border-t border-gray-200 dark:border-gray-700 my-1;
  }

  /* Table Components */
  .table {
    @apply w-full border-collapse bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-sm;
    background-color: var(--table-background);
    color: var(--color-text);
  }

  .table th,
  .table td {
    @apply px-4 py-3 text-left border-b border-gray-200 dark:border-gray-700 align-top;
    border-color: var(--table-border);
  }

  .table th {
    @apply font-semibold bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white text-sm uppercase tracking-wide;
    background-color: var(--table-header-background);
  }

  /* Notification Components */
  .notification {
    @apply px-4 py-3 rounded-md border-l-4 mb-4;
  }

  .notification.is-info {
    @apply bg-blue-50 dark:bg-blue-900 border-blue-500 text-blue-700 dark:text-blue-300;
  }

  .notification.is-success {
    @apply bg-success-50 dark:bg-success-900 border-success-500 text-success-700 dark:text-success-300;
  }

  .notification.is-warning {
    @apply bg-warning-50 dark:bg-warning-900 border-warning-500 text-warning-700 dark:text-warning-300;
  }

  .notification.is-danger {
    @apply bg-danger-50 dark:bg-danger-900 border-danger-500 text-danger-700 dark:text-danger-300;
  }

  /* Loading Components */
  .loading-spinner {
    @apply animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500;
  }

  .loading-overlay {
    @apply absolute inset-0 bg-white dark:bg-gray-900 bg-opacity-75 dark:bg-opacity-75 flex items-center justify-center;
  }
}
