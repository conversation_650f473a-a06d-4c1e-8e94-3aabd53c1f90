/* Main CSS Entry Point - Tailwind CSS Architecture */
/* This file imports Tailwind CSS and custom styles in the correct order */

/* 1. Tailwind Base Layer - Includes CSS reset, base styles, and theme variables */
@import './base.css';

/* 2. Tailwind Components Layer - Reusable component classes with @apply */
@import './components.css';

/* 3. Tailwind Utilities Layer - Custom utility classes and Tailwind utilities */
@import './utilities.css';

/* 4. Vendor Styles - Third-party library styles */
@import '@fortawesome/fontawesome-free/css/all.css';

/* Legacy styles that need to be preserved during transition */
/* These will be gradually converted to Tailwind classes */

/* Temporary compatibility styles */
.is-loading {
  @apply opacity-70 pointer-events-none;
}

.is-hidden {
  @apply hidden;
}

.is-invisible {
  @apply invisible;
}

/* Animation transitions for Vue */
.fade-enter-active,
.fade-leave-active {
  @apply transition-opacity duration-300;
}

.fade-enter-from,
.fade-leave-to {
  @apply opacity-0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  @apply transition-all duration-300;
}

.slide-up-enter-from {
  @apply opacity-0 translate-y-4;
}

.slide-up-leave-to {
  @apply opacity-0 -translate-y-4;
}

.slide-down-enter-active,
.slide-down-leave-active {
  @apply transition-all duration-300;
}

.slide-down-enter-from {
  @apply opacity-0 -translate-y-4;
}

.slide-down-leave-to {
  @apply opacity-0 translate-y-4;
}



