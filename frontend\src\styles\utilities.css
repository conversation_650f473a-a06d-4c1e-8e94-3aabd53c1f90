/* Tailwind Utilities Layer */
@tailwind utilities;

@layer utilities {
  /* Theme-aware utilities */
  .text-theme {
    color: var(--color-text);
  }

  .text-theme-strong {
    color: var(--color-text-strong);
  }

  .text-theme-muted {
    color: var(--color-text-muted);
  }

  .text-theme-light {
    color: var(--color-text-light);
  }

  .text-theme-inverse {
    color: var(--color-text-inverse);
  }

  .bg-theme {
    background-color: var(--color-background);
  }

  .bg-theme-surface {
    background-color: var(--color-surface);
  }

  .bg-theme-card {
    background-color: var(--color-card);
  }

  .border-theme {
    border-color: var(--color-border);
  }

  .border-theme-hover:hover {
    border-color: var(--color-border-hover);
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.5s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      transform: translateY(10px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slideDown {
    from {
      transform: translateY(-10px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes bounceIn {
    0% {
      transform: scale(0.3);
      opacity: 0;
    }
    50% {
      transform: scale(1.05);
    }
    70% {
      transform: scale(0.9);
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Layout utilities */
  .container-fluid {
    @apply w-full px-4 mx-auto;
  }

  .container-sm {
    @apply max-w-screen-sm mx-auto px-4;
  }

  .container-md {
    @apply max-w-screen-md mx-auto px-4;
  }

  .container-lg {
    @apply max-w-screen-lg mx-auto px-4;
  }

  .container-xl {
    @apply max-w-screen-xl mx-auto px-4;
  }

  /* Flexbox utilities */
  .flex-center {
    @apply flex items-center justify-center;
  }

  .flex-between {
    @apply flex items-center justify-between;
  }

  .flex-start {
    @apply flex items-center justify-start;
  }

  .flex-end {
    @apply flex items-center justify-end;
  }

  /* Grid utilities */
  .grid-auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .grid-auto-fill {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  /* Spacing utilities */
  .space-y-px > * + * {
    margin-top: 1px;
  }

  .space-x-px > * + * {
    margin-left: 1px;
  }

  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }

  .text-pretty {
    text-wrap: pretty;
  }

  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-ellipsis-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Interaction utilities */
  .hover-lift:hover {
    @apply transform -translate-y-1 shadow-lg transition-all duration-200;
  }

  .hover-scale:hover {
    @apply transform scale-105 transition-transform duration-200;
  }

  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .focus-ring-inset {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-inset;
  }

  /* State utilities */
  .is-loading {
    @apply opacity-70 pointer-events-none;
  }

  .is-disabled {
    @apply opacity-50 cursor-not-allowed pointer-events-none;
  }

  .is-hidden {
    @apply hidden !important;
  }

  .is-invisible {
    @apply invisible;
  }

  /* Scrollbar utilities */
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-none {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .scrollbar-none::-webkit-scrollbar {
    display: none;
  }

  .scrollbar-custom::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  .scrollbar-custom::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }

  .scrollbar-custom::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  }

  .scrollbar-custom::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }

  /* Print utilities */
  @media print {
    .print-hidden {
      display: none !important;
    }

    .print-visible {
      display: block !important;
    }
  }

  /* Accessibility utilities */
  .sr-only-focusable:not(:focus):not(:focus-within) {
    @apply sr-only;
  }

  .skip-link {
    @apply absolute -top-10 left-6 bg-primary-600 text-white px-4 py-2 rounded-md z-50 focus:top-6 transition-all;
  }

  /* Theme transition utilities */
  .theme-transition {
    transition: var(--transition-theme);
  }

  .theme-transition * {
    transition: var(--transition-theme);
  }

  /* Custom shadows */
  .shadow-theme-sm {
    box-shadow: var(--shadow-sm);
  }

  .shadow-theme-md {
    box-shadow: var(--shadow-md);
  }

  .shadow-theme-lg {
    box-shadow: var(--shadow-lg);
  }

  /* Border radius utilities */
  .rounded-theme {
    border-radius: var(--radius);
  }

  /* Responsive utilities for specific breakpoints */
  @media (max-width: 640px) {
    .mobile-hidden {
      display: none;
    }
  }

  @media (min-width: 641px) {
    .mobile-only {
      display: none;
    }
  }

  @media (max-width: 768px) {
    .tablet-hidden {
      display: none;
    }
  }

  @media (min-width: 769px) {
    .tablet-only {
      display: none;
    }
  }

  @media (max-width: 1024px) {
    .desktop-hidden {
      display: none;
    }
  }

  @media (min-width: 1025px) {
    .desktop-only {
      display: none;
    }
  }
}
