<template>
  <div class="flex flex-col h-screen overflow-hidden bg-gray-50 dark:bg-gray-900 transition-colors duration-300" :class="layoutClasses">
    <!-- Mobile Header -->
    <header class="md:hidden bg-primary-500 border-b border-gray-200 dark:border-gray-700 min-h-12 z-30">
      <nav class="h-full">
        <div class="flex items-center justify-between px-4 py-3">
          <div class="flex items-center cursor-pointer" @click="goToDashboard">
            <h1 class="text-xl font-semibold text-white">Notes</h1>
          </div>
          <button class="flex flex-col space-y-1" @click="toggleMobileSidebar">
            <span class="w-6 h-0.5 bg-white"></span>
            <span class="w-6 h-0.5 bg-white"></span>
            <span class="w-6 h-0.5 bg-white"></span>
          </button>
        </div>
      </nav>
    </header>

    <!-- Main Layout Container -->
    <div class="flex flex-1 overflow-hidden pt-12 md:pt-0">
      <!-- Sidebar Panel -->
      <aside class="flex-shrink-0 h-full bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 overflow-y-auto transition-all duration-300 z-30"
             :class="{
               'w-64': !isSidebarCollapsed || screenWidth < 769,
               'w-16': isSidebarCollapsed && screenWidth >= 769,
               'fixed top-12 bottom-0 left-0 w-16 shadow-lg z-40 md:relative md:top-0': screenWidth < 769,
               'translate-x-0': isSidebarOpen || screenWidth >= 769,
               '-translate-x-full': !isSidebarOpen && screenWidth < 769
             }">
        <Sidebar :is-collapsed="isSidebarCollapsed || (screenWidth < 769 && isSidebarOpen)" :current-section="currentSection" @toggle-collapse="toggleSidebarCollapse"
          @close-mobile="closeMobileSidebar" @create-note="handleCreateNote" @open-settings="openSettingsModal"
          @create-group="openCreateGroupModal" @show-dashboard="handleShowDashboard" @filter-by-tags="handleTagFilter"
          @update-tags="handleUpdateTags" @section-change="handleSectionChange" />
      </aside>

      <!-- Note List Panel -->
      <section class="flex-shrink-0 h-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 overflow-y-auto transition-all duration-300 z-10"
               :class="{
                 'hidden': isEditorFullscreen || (screenWidth < 769 && !isSidebarOpen),
                 'w-80': !isSidebarCollapsed || screenWidth < 769,
                 'w-72': isSidebarCollapsed && screenWidth >= 769,
                 'fixed top-12 left-16 w-80 bottom-0 shadow-lg z-30 md:relative md:top-0 md:left-0': screenWidth < 769 && isSidebarOpen
               }">
        <!-- Breadcrumb Navigation - Only show in 1-2 panel mode -->
        <BreadcrumbNavigation :show-breadcrumb="isSidebarCollapsed || screenWidth < 769" />

        <!-- Always show Note List -->
        <NoteList :section="currentSection" :tag-filter="activeTagFilter" @note-selected="handleNoteSelected"
          @create-note="handleCreateNote" @share-note="handleShareNote" />
      </section>

      <!-- Editor Panel -->
      <main class="flex-1 h-full bg-white dark:bg-gray-800 overflow-y-auto transition-all duration-300"
            :class="{ 'fixed inset-0 z-50': isEditorFullscreen }">
        <EditorPanel :selected-note="selectedNote" :is-fullscreen="isEditorFullscreen"
          :show-dashboard="currentSection === 'dashboard'" @toggle-fullscreen="toggleEditorFullscreen"
          @create-note="handleCreateNote" @note-saved="handleNoteSaved" @share-note="handleShareNote"
          @create-group="openCreateGroupModal" @open-search="openSearchModal" @open-settings="openSettingsModal" />
      </main>
    </div>

    <!-- Mobile Overlay -->
    <div v-if="isSidebarOpen" class="fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden" @click="closeMobileSidebar"></div>

    <!-- Global Components -->
    <KeyboardShortcuts @open-search="openSearchModal" @create-note="createNote" @toggle-sidebar="toggleMobileSidebar"
      @focus-editor="focusEditor" @save-note="saveCurrentNote" />

    <!-- Search Modal -->
    <SearchModal :is-open="isSearchModalOpen" @close="closeSearchModal" @note-selected="handleSearchNoteSelected"
      @create-note="handleCreateNote" />

    <!-- Share Modal -->
    <ShareModal :is-open="isShareModalOpen" :note="noteToShare" @close="closeShareModal"
      @share-created="handleShareCreated" />

    <!-- Settings Modal -->
    <SettingsModal :is-open="isSettingsModalOpen" @close="closeSettingsModal" />

    <!-- Create Group Modal -->
    <CreateGroupModal v-if="isCreateGroupModalOpen" @close="closeCreateGroupModal" @created="handleGroupCreated" />

    <!-- Auth Status Indicator -->
    <AuthStatusIndicator />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { useSettingsStore } from '../../stores/settings'
import { useTheme } from '../../composables/useTheme'
import Sidebar from './Sidebar.vue'
import NoteList from './NoteList.vue'
import EditorPanel from './EditorPanel.vue'
import BreadcrumbNavigation from '../navigation/BreadcrumbNavigation.vue'
import KeyboardShortcuts from '../navigation/KeyboardShortcuts.vue'
import SearchModal from '../navigation/SearchModal.vue'
import ShareModal from '../sharing/ShareModal.vue'
import SettingsModal from '../settings/SettingsModal.vue'
import CreateGroupModal from '../groups/CreateGroupModal.vue'
import AuthStatusIndicator from '../common/AuthStatusIndicator.vue'

// Composables - with safe initialization
let route: any = null
let router: any = null
const authStore = useAuthStore()
const settingsStore = useSettingsStore()
const {
  currentTheme,
  currentMode,
  isLoading: isThemeLoading,
  error: themeError,
  initializeThemeManager,
  setThemeMode,
  onThemeChange,
  offThemeChange
} = useTheme()

// Reactive state
const isSidebarOpen = ref(false)
const isSidebarCollapsed = ref(false)
const isEditorFullscreen = ref(false)
const selectedNote = ref<any>(null)
const screenWidth = ref(window.innerWidth)
const isSearchModalOpen = ref(false)
const isShareModalOpen = ref(false)
const isSettingsModalOpen = ref(false)
const isCreateGroupModalOpen = ref(false)
const noteToShare = ref<any>(null)
const routerReady = ref(false)
const activeTagFilter = ref<{ selectedTags: string[], filterMode: 'any' | 'all' } | null>(null)

// Local section state to prevent sidebar reloading
const currentSectionState = ref<string>('dashboard')

// Theme management state
const themeInitialized = ref(false)
const themeLoadingState = ref(false)

// Safe router initialization
const initializeRouter = () => {
  try {
    route = useRoute()
    router = useRouter()

    if (route && router) {
      routerReady.value = true
      console.log('Router initialized successfully')
    } else {
      console.warn('Router composables not fully available')
    }
  } catch (error) {
    console.warn('Router initialization error:', error)
    // Continue without router - use window.location as fallback
  }
}

// Try to initialize router immediately
try {
  initializeRouter()
} catch (error) {
  console.warn('Initial router setup failed, will retry:', error)
}

// Check if any modal should be opened based on route or user preferences
const checkModalStates = () => {
  try {
    // Check route first
    let routePath = window.location.pathname

    if (routerReady.value && route?.path) {
      routePath = route.path
    } else if (router?.currentRoute?.value?.path) {
      routePath = router.currentRoute.value.path
    }

    // Check if we should open settings modal
    if (routePath.includes('/settings')) {
      isSettingsModalOpen.value = true
      return
    }

    // Don't open modals automatically after login unless specifically requested
    // This prevents the settings modal from opening automatically after login
    const isLoginPath = routePath.includes('/login') || routePath.includes('/register') || routePath.includes('/auth')
    if (isLoginPath) {
      return
    }

    // Load user preferences for modals
    const modalPreferences = authStore.loadModalPreferences()

    // Check if settings modal should be opened
    if (modalPreferences.settingsModalOpen) {
      // Only open if not on login/register page
      isSettingsModalOpen.value = true
    } else if (modalPreferences.searchModalOpen) {
      isSearchModalOpen.value = true
    } else if (modalPreferences.shareModalOpen && noteToShare.value) {
      isShareModalOpen.value = true
    }
  } catch (error) {
    console.warn('Failed to check modal states:', error)
  }
}

// Watch route changes
const routeWatcher = watch(
  () => routerReady.value && route?.path,
  (newPath) => {
    if (newPath && newPath.includes('/settings')) {
      openSettingsModal()
    }
  }
)

// Computed properties
const layoutClasses = computed(() => ({
  'is-mobile': screenWidth.value < 769,
  'is-tablet': screenWidth.value >= 769 && screenWidth.value < 1024,
  'is-desktop': screenWidth.value >= 1024,
  'sidebar-collapsed': isSidebarCollapsed.value,
  'editor-fullscreen': isEditorFullscreen.value
}))

const currentSection = computed(() => {
  // Use local state to prevent sidebar reloading
  return currentSectionState.value
})

// Methods
const toggleMobileSidebar = () => {
  console.log('Toggle mobile sidebar clicked. Current state:', isSidebarOpen.value)
  isSidebarOpen.value = !isSidebarOpen.value
  console.log('New state:', isSidebarOpen.value)
}

const closeMobileSidebar = () => {
  isSidebarOpen.value = false
}

const toggleSidebarCollapse = () => {
  isSidebarCollapsed.value = !isSidebarCollapsed.value
}

const toggleEditorFullscreen = () => {
  isEditorFullscreen.value = !isEditorFullscreen.value
}

const handleNoteSelected = (note: any) => {
  selectedNote.value = note
  // On mobile, close sidebar when note is selected
  if (screenWidth.value < 769) {
    closeMobileSidebar()
  }
}

const handleResize = () => {
  screenWidth.value = window.innerWidth

  // Auto-collapse sidebar on tablets
  if (screenWidth.value >= 769 && screenWidth.value < 1024) {
    isSidebarCollapsed.value = true
  } else if (screenWidth.value >= 1024) {
    isSidebarCollapsed.value = false
  }
}

const handleCreateNote = (type?: string) => {
  createNote(type)
}

const createNote = async (type: string = 'richtext') => {
  // Create new note logic
  console.log('Creating new note:', type)

  // This would typically call the notes store to create a new note
  // For now, just create a placeholder
  const newNote = {
    id: `new-${Date.now()}`,
    title: 'New Note',
    content: '',
    noteType: type,
    isNew: true,
    isCreating: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }

  // Set the newly created note as selected
  selectedNote.value = newNote
}

const handleNoteSaved = (note: any) => {
  // Update the selected note with saved data
  selectedNote.value = note
}

const focusEditor = () => {
  // Focus the editor if a note is selected
  if (selectedNote.value) {
    const editorElement = document.querySelector('.editor-area, .editor-textarea')
    if (editorElement) {
      (editorElement as HTMLElement).focus()
    }
  }
}

const saveCurrentNote = () => {
  // Save the current note if one is selected
  if (selectedNote.value) {
    console.log('Save current note:', selectedNote.value.id)
    // This would trigger the save functionality
  }
}

const handleShareNote = (note: any) => {
  noteToShare.value = note
  isShareModalOpen.value = true
  // Save state to user preferences
  authStore.saveModalPreferences({ shareModalOpen: true })
}

const closeShareModal = () => {
  isShareModalOpen.value = false
  noteToShare.value = null
  // Save state to user preferences
  authStore.saveModalPreferences({ shareModalOpen: false })
}

const handleShareCreated = (share: any) => {
  console.log('Share created:', share)
  // You might want to show a success notification here
}

const openSettingsModal = () => {
  isSettingsModalOpen.value = true
  // Save state to user preferences
  authStore.saveModalPreferences({ settingsModalOpen: true })
}

const closeSettingsModal = () => {
  isSettingsModalOpen.value = false
  // Save state to user preferences
  authStore.saveModalPreferences({ settingsModalOpen: false })
}

const openCreateGroupModal = () => {
  isCreateGroupModalOpen.value = true
}

const closeCreateGroupModal = () => {
  isCreateGroupModalOpen.value = false
}

const handleGroupCreated = (group: any) => {
  closeCreateGroupModal()
  // Navigate to the newly created group
  if (router && router.push) {
    router.push(`/groups/${group.id}`)
  }
}

const handleShowDashboard = () => {
  // Clear selected note to show dashboard in editor panel
  selectedNote.value = null
}

const handleSectionChange = (section: string) => {
  console.log('Handling section change to:', section)
  
  // Clear selected note to show dashboard in editor panel
  selectedNote.value = null
  
  // Update local state to prevent sidebar reloading
  currentSectionState.value = section
  
  // Update the URL without triggering a full route change
  // Handle dashboard as special case to avoid /dashboard/dashboard
  if (router && router.replace) {
    const targetPath = section === 'dashboard' ? '/dashboard' : `/dashboard/${section}`
    router.replace(targetPath)
  } else {
    // Fallback: update URL without page reload
    const newUrl = section === 'dashboard' ? '/dashboard' : `/dashboard/${section}`
    if (window.history && window.history.replaceState) {
      window.history.replaceState({ path: newUrl }, '', newUrl)
    }
  }
}

// Reset all modal states
const resetModalStates = () => {
  try {
    // Close all modals
    isSettingsModalOpen.value = false
    isSearchModalOpen.value = false
    isShareModalOpen.value = false
    isCreateGroupModalOpen.value = false
    noteToShare.value = null

    // Clear all modal states from user preferences
    authStore.saveModalPreferences({
      settingsModalOpen: false,
      searchModalOpen: false,
      shareModalOpen: false
    })
  } catch (error) {
    console.warn('Failed to reset modal states:', error)
  }
}

// Initialize theme system
const initializeTheme = async () => {
  if (themeInitialized.value) {
    console.log('Theme already initialized, skipping...')
    return
  }

  themeLoadingState.value = true

  try {
    // Initialize theme manager
    await initializeThemeManager()

    // Initialize settings store (which loads theme preferences)
    await settingsStore.initializeSettings()

    // Load theme preferences from auth store
    const themePrefs = authStore.loadThemePreference()

    // Apply theme based on preferences (without syncing to backend during initialization)
    const { setTheme, setThemeMode } = useTheme()

    if (themePrefs.mode === 'auto') {
      await setThemeMode('auto')
      settingsStore.setLocalThemeState('auto')
    } else if (themePrefs.themeName) {
      await setTheme(themePrefs.themeName)
      // Determine the correct mode for the theme
      const darkThemes = ['darkly', 'cyborg', 'slate', 'superhero', 'vapor']
      const mode = darkThemes.includes(themePrefs.themeName) ? 'dark' : 'light'
      settingsStore.setLocalThemeState(mode, themePrefs.themeName)
    } else {
      await setThemeMode(themePrefs.mode)
      settingsStore.setLocalThemeState(themePrefs.mode)
    }

    themeInitialized.value = true
    console.log('Theme system initialized successfully')
  } catch (error) {
    console.error('Failed to initialize theme system:', error)
    settingsStore.handleThemeError('Failed to initialize theme system')

    // Fallback to basic theme application
    applyBasicTheme()
    themeInitialized.value = true
  } finally {
    themeLoadingState.value = false
  }
}

// Fallback theme application (legacy method)
const applyBasicTheme = () => {
  try {
    const themePrefs = authStore.loadThemePreference()
    const html = document.documentElement

    // Use specific theme name if available, otherwise use fallback
    const themeName = themePrefs.themeName || (themePrefs.mode === 'dark' ? 'darkly' : 'default')

    if (themePrefs.mode === 'dark') {
      html.classList.add('dark')
      html.classList.remove('light')
      html.setAttribute('data-theme', themeName)
    } else if (themePrefs.mode === 'light') {
      html.classList.add('light')
      html.classList.remove('dark')
      html.setAttribute('data-theme', themeName)
    } else {
      // Auto mode - use system preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      html.classList.toggle('dark', prefersDark)
      html.classList.toggle('light', !prefersDark)
      html.setAttribute('data-theme', prefersDark ? (themePrefs.themeName || 'darkly') : (themePrefs.themeName || 'default'))
    }

    console.log(`Fallback theme applied: ${html.getAttribute('data-theme')} (mode: ${themePrefs.mode})`)
  } catch (error) {
    console.warn('Failed to apply basic theme:', error)
    // Default to light theme on error
    const html = document.documentElement
    html.classList.add('light')
    html.classList.remove('dark')
    html.setAttribute('data-theme', 'default')
  }
}

// Handle theme changes
const handleThemeChange = (newTheme: string) => {
  console.log('Theme changed to:', newTheme)

  // Update document classes and attributes
  const html = document.documentElement
  const theme = settingsStore.availableThemes.find(t => t.name === newTheme)

  if (theme) {
    html.classList.toggle('dark', theme.isDark)
    html.classList.toggle('light', !theme.isDark)
    html.setAttribute('data-theme', newTheme)
  } else {
    // Fallback for unknown themes
    const isDark = newTheme === 'darkly' || newTheme.includes('dark')
    html.classList.toggle('dark', isDark)
    html.classList.toggle('light', !isDark)
    html.setAttribute('data-theme', newTheme)
  }

  console.log(`Theme change applied: data-theme="${newTheme}", classes="${html.className}"`)
}

// Handle theme loading states
const handleThemeLoadingState = (loading: boolean) => {
  themeLoadingState.value = loading

  if (loading) {
    // Add loading indicator class
    document.documentElement.classList.add('theme-loading')
  } else {
    // Remove loading indicator class
    document.documentElement.classList.remove('theme-loading')
  }
}

// Handle theme errors
const handleThemeErrorState = (error: string | null) => {
  if (error) {
    console.error('Theme error in AppLayout:', error)
    // Could show a user notification here
  }
}

// Emergency navigation function
const goToDashboard = () => {
  try {
    // Reset all modal states
    resetModalStates()

    // Ensure auth store is properly initialized before navigation
    if (!authStore.isInitialized && authStore.token) {
      console.log('Initializing auth before dashboard navigation...')
      authStore.initializeAuth()
    }

    if (router && router.push) {
      router.push('/dashboard')
    } else {
      // Fallback: direct navigation
      window.location.href = '/dashboard'
    }
  } catch (error) {
    console.error('Navigation error, using fallback:', error)
    window.location.href = '/dashboard'
  }
}

const openSearchModal = () => {
  isSearchModalOpen.value = true
  // Save state to user preferences
  authStore.saveModalPreferences({ searchModalOpen: true })
}

const closeSearchModal = () => {
  isSearchModalOpen.value = false
  // Save state to user preferences
  authStore.saveModalPreferences({ searchModalOpen: false })
}

const handleSearchNoteSelected = (note: any) => {
  selectedNote.value = note
  closeSearchModal()
}

const handleTagFilter = (filter: { selectedTags: string[], filterMode: 'any' | 'all' }) => {
  activeTagFilter.value = filter.selectedTags.length > 0 ? filter : null
  console.log('Tag filter applied:', filter)
}

const handleUpdateTags = (tags: string[]) => {
  // This would be called when the navigation changes to update available tags
  console.log('Tags updated from navigation:', tags)
}



// Lifecycle
onMounted(async () => {
  window.addEventListener('resize', handleResize)

  // Set initial sidebar state based on screen size
  if (screenWidth.value >= 1024) {
    isSidebarCollapsed.value = false
  } else if (screenWidth.value >= 769) {
    isSidebarCollapsed.value = true
  }

  // Initialize router if not already done
  if (!routerReady.value) {
    nextTick(() => {
      initializeRouter()
    })
  }

  // Initialize section state based on current route
  nextTick(() => {
    try {
      let routePath = window.location.pathname
      
      // Try to get from Vue Router if available
      if (routerReady.value && route?.path) {
        routePath = route.path
      } else if (router?.currentRoute?.value?.path) {
        routePath = router.currentRoute.value.path
      }

      // Set initial section based on route
      if (routePath.includes('/recent')) {
        currentSectionState.value = 'recent'
      } else if (routePath.includes('/favorites')) {
        currentSectionState.value = 'favorites'
      } else if (routePath.includes('/archived')) {
        currentSectionState.value = 'archived'
      } else if (routePath.includes('/shared')) {
        currentSectionState.value = 'shared'
      } else if (routePath.includes('/notes')) {
        currentSectionState.value = 'all-notes'
      } else {
        currentSectionState.value = 'dashboard'
      }
      
      console.log('Initialized section state:', currentSectionState.value)
    } catch (error) {
      console.warn('Failed to initialize section state:', error)
      currentSectionState.value = 'dashboard'
    }
  })

  // Initialize theme system
  await initializeTheme()

  // Set up theme change listeners
  onThemeChange(handleThemeChange)

  // Watch for theme loading state changes
  watch(() => Boolean(isThemeLoading as boolean), handleThemeLoadingState)
  watch(() => settingsStore.themeError, handleThemeErrorState)

  // Listen for system theme changes
  // System theme changes are handled automatically by useTheme composable
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
  mediaQuery.addEventListener('change', () => {
    // The useTheme composable's system preference watcher will handle auto mode updates
    console.log('System theme preference changed, useTheme will handle auto mode')
  })

  // Check if any modal should be opened
  nextTick(() => {
    checkModalStates()
  })

  // Listen for logout events to clear modal states
  const logoutHandler = () => {
    try {
      // Clear all modal states from localStorage
      localStorage.removeItem('settingsModalOpen')
      localStorage.removeItem('searchModalOpen')
      localStorage.removeItem('shareModalOpen')

      // Close all modals
      isSettingsModalOpen.value = false
      isSearchModalOpen.value = false
      isShareModalOpen.value = false
      noteToShare.value = null

      // Reset theme initialization flag to allow re-initialization on next login
      themeInitialized.value = false
    } catch (error) {
      console.warn('Failed to clear modal states on logout:', error)
    }
  }

  window.addEventListener('auth-logout-complete', logoutHandler)

  // Listen for theme errors
  const themeErrorHandler = (event: CustomEvent) => {
    console.error('Theme error event:', event.detail.message)
    // Could show user notification here
  }

  window.addEventListener('theme-error', themeErrorHandler as EventListener)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)

  // Clean up theme listeners
  offThemeChange(handleThemeChange)

  // Clean up system theme listener
  // Note: systemThemeHandler cleanup is handled in onMounted
})
</script>

<style scoped>
/* Custom scrollbar styling for panels */
aside::-webkit-scrollbar,
section::-webkit-scrollbar,
main::-webkit-scrollbar {
  width: 6px;
}

aside::-webkit-scrollbar-track,
section::-webkit-scrollbar-track,
main::-webkit-scrollbar-track {
  background-color: #f3f4f6;
  border-radius: 0.25rem;
}

aside::-webkit-scrollbar-thumb,
section::-webkit-scrollbar-thumb,
main::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 0.25rem;
}

aside::-webkit-scrollbar-thumb:hover,
section::-webkit-scrollbar-thumb:hover,
main::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

/* Dark mode scrollbar styles */
.dark aside::-webkit-scrollbar-track,
.dark section::-webkit-scrollbar-track,
.dark main::-webkit-scrollbar-track {
  background-color: #374151;
}

.dark aside::-webkit-scrollbar-thumb,
.dark section::-webkit-scrollbar-thumb,
.dark main::-webkit-scrollbar-thumb {
  background-color: #4b5563;
}

.dark aside::-webkit-scrollbar-thumb:hover,
.dark section::-webkit-scrollbar-thumb:hover,
.dark main::-webkit-scrollbar-thumb:hover {
  background-color: #6b7280;
}

/* Ensure proper touch scrolling on mobile */
aside,
section,
main {
  -webkit-overflow-scrolling: touch;
}

/* Print styles */
@media print {
  header,
  aside,
  section,
  .fixed {
    display: none !important;
  }

  main {
    width: 100% !important;
    height: auto !important;
    overflow: visible !important;
    position: static !important;
  }
}
</style>
